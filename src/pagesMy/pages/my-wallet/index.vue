<template>
  <view class="wallet-page bg-#fafafa min-h-screen">
    <!-- 顶部余额卡片 -->
    <view class="mx-4 pt-4 pb-6">
      <view
        class="relative overflow-hidden rounded-lg p-6"
        style="background: linear-gradient(135deg, #4a90e2 0%, #5b9bd5 100%)"
      >
        <!-- 背景装饰 -->
        <view class="absolute -top-8 -right-8 w-32 h-32 bg-white opacity-10 rounded-full"></view>
        <view class="absolute -bottom-4 -right-4 w-20 h-20 bg-white opacity-10 rounded-full"></view>

        <view class="flex items-center justify-between relative z-10">
          <view>
            <text class="text-white text-28rpx opacity-90 mb-2 block">我的余额</text>
            <text class="text-white text-72rpx font-bold">¥{{ balance }}</text>
          </view>
          <wd-button
            type="text"
            size="small"
            custom-style="color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 20px; padding: 8px 16px;"
            @click="handleViewDetails"
          >
            明细
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 充值金额选择 -->
    <view class="mx-4 mb-6">
      <text class="text-32rpx font-medium text-#333 mb-4 block">充值金额</text>
      <view class="grid grid-cols-2 gap-4">
        <view
          v-for="(item, index) in rechargeOptions"
          :key="index"
          class="relative bg-white rounded-lg p-4 border-2 transition-all duration-200"
          :class="selectedAmount === item.amount ? 'border-#4A90E2 bg-#f8fbff' : 'border-#e0e0e0'"
          @click="handleSelectAmount(item.amount)"
        >
          <!-- 选中状态图标 -->
          <view
            v-if="selectedAmount === item.amount"
            class="absolute top-2 right-2 w-5 h-5 bg-#4A90E2 rounded-full flex items-center justify-center"
          >
            <wd-icon name="check" size="12px" color="white" />
          </view>

          <view class="mb-2">
            <text class="text-48rpx font-bold text-#333">{{ item.amount }}</text>
            <text class="text-24rpx text-#666 ml-1">元</text>
          </view>
          <text class="text-24rpx text-#999">充值赠送{{ item.couponCount }}张优惠券</text>
        </view>
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view
      class="fixed bottom-0 left-0 right-0 bg-white border-t border-#e0e0e0 px-4 py-4"
      style="padding-bottom: calc(16rpx + env(safe-area-inset-bottom))"
    >
      <!-- 协议复选框 -->
      <view class="flex items-center mb-4">
        <wd-checkbox v-model="agreeProtocol" size="small" custom-class="mr-2" />
        <text class="text-24rpx text-#666">
          同意
          <text class="text-#4A90E2 underline" @click="handleViewProtocol">《充值服务协议》</text>
        </text>
      </view>

      <!-- 立即充值按钮 -->
      <wd-button
        type="primary"
        size="large"
        block
        :disabled="!selectedAmount || !agreeProtocol"
        :loading="recharging"
        @click="handleRecharge"
        custom-style="background-color: #4A90E2; border-color: #4A90E2; border-radius: 8px;"
      >
        立即充值
      </wd-button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getWalletBalance, submitRecharge } from '@/pagesMy/api/index.js'
import { checkPayEnvironment } from '@/utils/utils.js'

// 余额数据
const balance = ref('509.50')
// 选中的充值金额
const selectedAmount = ref('')
// 是否同意协议
const agreeProtocol = ref(false)
// 充值中状态
const recharging = ref(false)

// 充值选项配置
const rechargeOptions = ref([
  { amount: '500', couponCount: 1 },
  { amount: '1000', couponCount: 3 },
  { amount: '5000', couponCount: 5 },
  { amount: '10000', couponCount: 8 },
])

// 页面加载
onMounted(() => {
  loadWalletBalance()
})

// 加载钱包余额
const loadWalletBalance = async () => {
  try {
    const res = await getWalletBalance()
    if (res.data && res.data.balance) {
      balance.value = res.data.balance
    }
  } catch (error) {
    console.error('获取余额失败:', error)
  }
}

// 选择充值金额
const handleSelectAmount = (amount) => {
  selectedAmount.value = amount
}

// 查看明细
const handleViewDetails = () => {
  uni.navigateTo({
    url: '/pagesMy/pages/wallet-details/index',
  })
}

// 查看协议
const handleViewProtocol = () => {
  uni.navigateTo({
    url: '/pagesMy/pages/recharge-protocol/index',
  })
}

// 立即充值
const handleRecharge = async () => {
  if (!selectedAmount.value) {
    uni.showToast({
      title: '请选择充值金额',
      icon: 'none',
    })
    return
  }

  if (!agreeProtocol.value) {
    uni.showToast({
      title: '请先同意充值服务协议',
      icon: 'none',
    })
    return
  }

  try {
    recharging.value = true

    // 检查支付环境
    const payEnv = await checkPayEnvironment()

    // 提交充值订单
    const orderRes = await submitRecharge({
      amount: selectedAmount.value,
      payType: payEnv.typeCode,
    })

    if (orderRes.data && orderRes.data.orderId) {
      // 调用支付
      await handlePay(orderRes.data)
    }
  } catch (error) {
    console.error('充值失败:', error)
    uni.showToast({
      title: error.message || '充值失败，请重试',
      icon: 'none',
    })
  } finally {
    recharging.value = false
  }
}

// 处理支付
const handlePay = async (orderData) => {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: orderData.provider,
      orderInfo: orderData.orderInfo,
      success: (res) => {
        uni.showToast({
          title: '充值成功',
          icon: 'success',
        })
        // 刷新余额
        loadWalletBalance()
        // 重置选择状态
        selectedAmount.value = ''
        agreeProtocol.value = false
        resolve(res)
      },
      fail: (err) => {
        uni.showToast({
          title: '支付取消或失败',
          icon: 'none',
        })
        reject(err)
      },
    })
  })
}
</script>

<style lang="scss" scoped></style>
