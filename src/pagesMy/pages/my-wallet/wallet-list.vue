<template>
  <z-paging
    ref="paging"
    v-model="recordList"
    @query="queryList"
    empty-view-text="暂无交易记录"
    class="bg-#fafafa"
  >
    <!-- 交易记录列表 -->
    <view
      v-for="(record, index) in recordList"
      :key="record.id"
      class="mx-4 mt-4 bg-white rounded-lg p-4"
      @click="handleRecordClick(record)"
    >
      <!-- 交易记录内容 -->
      <view class="flex items-center justify-between">
        <!-- 左侧：时间和金额 -->
        <view class="flex-1">
          <!-- 交易时间 -->
          <text class="text-24rpx text-#999 mb-2 block">{{ record.createTime }}</text>

          <!-- 交易金额 -->
          <view class="flex items-center">
            <text class="text-48rpx font-bold mr-2" :class="getAmountColorClass(record.type)">
              {{ getAmountDisplay(record.amount, record.type) }}
            </text>
          </view>
        </view>

        <!-- 右侧：交易类型标签 -->
        <view class="flex-shrink-0">
          <view
            class="px-3 py-1 rounded-full text-24rpx font-medium"
            :class="getTypeTagClass(record.type)"
          >
            {{ getTypeText(record.type) }}
          </view>
        </view>
      </view>

      <!-- 交易详情（可选） -->
      <view v-if="record.remark" class="mt-3 pt-3 border-t border-#f0f0f0">
        <text class="text-24rpx text-#666">{{ record.remark }}</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup>
import { ref } from 'vue'
import { getRechargeRecords } from '@/pagesMy/api/index.js'

// 分页组件引用
const paging = ref(null)
// 交易记录列表数据
const recordList = ref([])

// 交易类型映射
const typeMap = {
  1: '充值',
  2: '支出',
  3: '退款',
  4: '提现',
  5: '转账',
}

// 获取交易类型文字
const getTypeText = (type) => {
  return typeMap[type] || '其他'
}

// 获取交易类型标签样式
const getTypeTagClass = (type) => {
  const classMap = {
    1: 'bg-#e6f3ff text-#4A90E2', // 充值 - 蓝色
    2: 'bg-#ffe6e6 text-#ff4d4f', // 支出 - 红色
    3: 'bg-#f0e6ff text-#722ed1', // 退款 - 紫色
    4: 'bg-#fff7e6 text-#fa8c16', // 提现 - 橙色
    5: 'bg-#e6fff0 text-#52c41a', // 转账 - 绿色
  }
  return classMap[type] || 'bg-#f5f5f5 text-#666'
}

// 获取金额颜色样式
const getAmountColorClass = (type) => {
  // 支出类型显示红色，收入类型显示黑色
  return type === 2 ? 'text-#333' : 'text-#333'
}

// 获取金额显示格式
const getAmountDisplay = (amount, type) => {
  const formattedAmount = parseFloat(amount).toFixed(2)
  // 支出显示负号，收入显示正号
  if (type === 2) {
    return `- ¥${formattedAmount}`
  } else {
    return `+ ¥${formattedAmount}`
  }
}

// 查询交易记录列表
const queryList = async (pageNo, pageSize) => {
  try {
    const res = await getRechargeRecords({
      current: pageNo,
      size: pageSize,
    })

    // 模拟数据（实际开发中删除这部分）
    if (pageNo === 1) {
      const mockData = [
        {
          id: '1',
          createTime: '2025-06-09 10:23:34',
          amount: '509.50',
          type: 2, // 支出
          remark: '购买商品',
        },
        {
          id: '2',
          createTime: '2025-06-08 14:23:34',
          amount: '500.00',
          type: 1, // 充值
          remark: '钱包充值',
        },
        {
          id: '3',
          createTime: '2025-06-07 19:23:34',
          amount: '12.50',
          type: 3, // 退款
          remark: '订单退款',
        },
        {
          id: '4',
          createTime: '2025-06-06 21:23:34',
          amount: '500.00',
          type: 1, // 充值
          remark: '钱包充值',
        },
      ]
      paging.value.complete(mockData)
      return
    }

    const pageData = res.data.records || []
    paging.value.complete(pageData)
  } catch (error) {
    console.error('获取交易记录失败:', error)
    paging.value.complete(false)
  }
}

// 处理交易记录点击事件
const handleRecordClick = (record) => {
  console.log('点击交易记录:', record)
  // 这里可以添加查看交易详情的逻辑
  uni.navigateTo({
    url: `/pagesMy/pages/transaction-detail/index?id=${record.id}`,
  })
}
</script>

<style lang="scss" scoped></style>
