<template>
  <view class="flex flex-col h-100vh w-full bg-#fafafa">
    <!-- Tab导航 -->
    <wd-tabs v-model="activeTab" @change="handleTabChange">
      <wd-tab title="预定订单" name="0" />
      <wd-tab title="商城订单" name="1" />
      <wd-tab title="旅行订单" name="2" />
    </wd-tabs>

    <!-- 统一的订单列表 -->
    <view class="tab-content h-full flex-1">
      <z-paging
        ref="orderPaging"
        v-model="orderList"
        @query="queryOrders"
        :empty-view-text="getEmptyText()"
        :fixed="false"
        :auto="false"
      >
        <!-- 动态渲染不同类型的订单卡片 -->
        <view
          v-for="order in orderList"
          :key="`${order.orderType}-${order.id}`"
          class="mx-4 mb-3 bg-white rounded-lg overflow-hidden shadow-sm"
        >
          <!-- 酒店订单 -->
          <view v-if="order.orderType === 'hotel'" class="p-4">
            <!-- 酒店信息头部 -->
            <view class="flex items-center justify-between mb-3">
              <view class="flex items-center">
                <wd-img
                  src="/pagesShop/static/mall.png"
                  width="40rpx"
                  height="40rpx"
                  custom-class="mr-2"
                />
                <text class="text-32rpx font-medium text-#333">{{ order.hotel_name }}</text>
              </view>
              <text
                class="text-28rpx px-2 py-1 rounded"
                :class="getOrderStatusClass(order.order_status)"
              >
                {{ getOrderStatusText(order.order_status) }}
              </text>
            </view>

            <!-- 房间信息 -->
            <view class="flex mb-3">
              <wd-img
                :src="order.room_imge ? order.room_imge.split(',')[0] : ''"
                width="210rpx"
                height="160rpx"
                radius="8rpx"
                mode="aspectFill"
              >
                <template #error>
                  <view
                    class="w-210rpx h-160rpx bg-#f5f5f5 flex items-center justify-center rounded-lg"
                  >
                    <text class="text-#ccc text-xs">暂无图片</text>
                  </view>
                </template>
              </wd-img>

              <view class="flex-1 pl-3">
                <text class="text-32rpx text-#333 font-medium mb-2 block">
                  {{ order.room_name }}
                </text>
                <text class="text-28rpx text-#666 mb-1 block">
                  {{ order.check_in_date }} 至 {{ order.check_out_date }}
                </text>
                <view class="flex items-center justify-between mt-3">
                  <text class="text-red-500 text-36rpx font-bold">¥{{ order.total_amount }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 商城订单 -->
          <view v-else-if="order.orderType === 'mall'" class="p-4">
            <!-- 酒店信息头部 -->
            <view class="flex items-center justify-between mb-3">
              <view class="flex items-center">
                <wd-img
                  src="/pagesShop/static/mall.png"
                  width="40rpx"
                  height="40rpx"
                  custom-class="mr-2"
                />
                <text class="text-32rpx font-medium text-#333">{{ order.hotel_name }}</text>
              </view>
              <text
                class="text-28rpx px-2 py-1 rounded"
                :class="getOrderStatusClass(order.order_status)"
              >
                {{ getOrderStatusText(order.order_status) }}
              </text>
            </view>

            <!-- 商品列表 -->
            <view v-for="(item, index) in order.goods_list" :key="index" class="flex mb-3">
              <wd-img
                :src="item.imge ? item.imge.split(',')[0] : ''"
                width="160rpx"
                height="160rpx"
                radius="8rpx"
                mode="aspectFill"
              >
                <template #error>
                  <view
                    class="w-160rpx h-160rpx bg-#f5f5f5 flex items-center justify-center rounded-lg"
                  >
                    <text class="text-#ccc text-xs">暂无图片</text>
                  </view>
                </template>
              </wd-img>

              <view class="flex-1 pl-3">
                <text class="text-32rpx text-#333 mb-2 block text-ellipsis-2">
                  {{ item.name }}
                </text>
                <view class="flex items-center justify-between">
                  <text class="text-red-500 text-32rpx">¥{{ item.price }}</text>
                  <text class="text-28rpx text-#666">x{{ item.sum }}</text>
                </view>
              </view>
            </view>

            <!-- 订单总价 -->
            <view class="flex items-center justify-between pt-3 border-t border-#f0f0f0">
              <text class="text-28rpx text-#666">
                共{{ order.goods_list ? order.goods_list.length : 0 }}件商品
              </text>
              <text class="text-red-500 text-36rpx font-bold">¥{{ order.total_amount }}</text>
            </view>
          </view>

          <!-- 旅行订单 -->
          <view v-else-if="order.orderType === 'travel'" class="p-4">
            <!-- 订单信息头部 -->
            <view class="flex items-center justify-between mb-3">
              <view class="flex items-center">
                <wd-img
                  src="/pagesShop/static/mall.png"
                  width="40rpx"
                  height="40rpx"
                  custom-class="mr-2"
                />
                <text class="text-32rpx font-medium text-#333">旅行订单</text>
              </view>
              <text
                class="text-28rpx px-2 py-1 rounded"
                :class="getOrderStatusClass(order.order_status)"
              >
                {{ getOrderStatusText(order.order_status) }}
              </text>
            </view>

            <!-- 旅行订单内容 -->
            <view class="flex mb-3">
              <wd-img
                :src="order.imge ? order.imge.split(',')[0] : ''"
                width="210rpx"
                height="160rpx"
                radius="8rpx"
                mode="aspectFill"
              >
                <template #error>
                  <view
                    class="w-210rpx h-160rpx bg-#f5f5f5 flex items-center justify-center rounded-lg"
                  >
                    <text class="text-#ccc text-xs">暂无图片</text>
                  </view>
                </template>
              </wd-img>

              <view class="flex-1 pl-3">
                <text class="text-32rpx text-#333 font-medium mb-2 block">
                  {{ order.name || '旅行套餐' }}
                </text>
                <text class="text-28rpx text-#666 mb-1 block">
                  {{ order.travel_date || '待确定' }}
                </text>
                <view class="flex items-center justify-between mt-3">
                  <text class="text-red-500 text-36rpx font-bold">
                    ¥{{ order.total_amount || 0 }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getHotelOrderList, getMallOrderList } from '../../api'

// Tab状态
const activeTab = ref(0)

// 分页组件引用
const orderPaging = ref(null)

// 统一的订单列表数据
const orderList = ref([])

// 页面初始化完成标识
const isInitialized = ref(false)

// 订单状态映射
const orderStatusMap = {
  0: '待支付',
  1: '已支付',
  2: '已消费',
  3: '已退款',
  4: '已取消',
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  return orderStatusMap[status] || '未知状态'
}

// 获取订单状态样式类
const getOrderStatusClass = (status) => {
  const statusClasses = {
    0: 'bg-orange-100 text-orange-600', // 待支付
    1: 'bg-blue-100 text-blue-600', // 已支付
    2: 'bg-green-100 text-green-600', // 已消费
    3: 'bg-gray-100 text-gray-600', // 已退款
    4: 'bg-red-100 text-red-600', // 已取消
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-600'
}

// 统一的订单查询方法
const queryOrders = async (pageNo, pageSize) => {
  // 确保初始化完成
  if (!isInitialized.value) {
    if (orderPaging.value) {
      orderPaging.value.complete([], false)
    }
    return
  }

  try {
    let res = null
    let pageData = []

    console.log(activeTab.value, 'activeTab', pageNo, pageSize)

    // 根据当前选中的 tab 调用不同的 API
    switch (Number(activeTab.value)) {
      case 0: // 预定订单（酒店订单）
        res = await getHotelOrderList({
          current: pageNo,
          size: pageSize,
        })
        console.log(res, 'res')
        pageData = res.data?.records || []
        // 为酒店订单添加类型标识
        pageData = pageData.map((order) => ({
          ...order,
          orderType: 'hotel',
        }))
        break

      case 1: // 商城订单
        res = await getMallOrderList({
          current: pageNo,
          size: pageSize,
        })
        pageData = res.data?.records || []
        // 为商城订单添加类型标识
        pageData = pageData.map((order) => ({
          ...order,
          orderType: 'mall',
        }))
        break

      case 2: // 旅行订单（暂时返回空数据）
        await new Promise((resolve) => setTimeout(resolve, 500)) // 模拟请求延迟
        pageData = []
        break

      default:
        pageData = []
    }

    if (orderPaging.value) {
      orderPaging.value.complete(pageData)
    }
  } catch (error) {
    console.error('查询订单失败:', error)
    if (orderPaging.value) {
      orderPaging.value.complete(false)
    }
    uni.showToast({
      title: '加载订单失败',
      icon: 'none',
    })
  }
}

// 获取空数据提示文本
const getEmptyText = () => {
  const emptyTexts = ['暂无预定订单', '暂无商城订单', '暂无旅行订单']
  return emptyTexts[activeTab.value] || '暂无订单数据'
}

// Tab切换处理
const handleTabChange = ({ index }) => {
  console.log(index, 'index')
  activeTab.value = index

  // 切换 Tab 时重新加载数据
  if (isInitialized.value) {
    nextTick(() => {
      orderPaging.value?.reload()
    })
  }
}

// 初始化页面数据
const initPageData = async () => {
  // try {
  //   // 标记初始化完成
  //   isInitialized.value = true
  //   // 等待组件完全渲染后再加载数据
  //   await nextTick()
  //   // 触发数据加载
  //   if (orderPaging.value) {
  //     orderPaging.value.reload()
  //   }
  // } catch (error) {
  //   console.error('初始化页面失败:', error)
  //   uni.showToast({
  //     title: '页面初始化失败',
  //     icon: 'none',
  //   })
  // }
}

// 页面生命周期
onLoad(() => {
  console.log('我的订单页面加载')
  initPageData()
})
</script>

<style lang="scss" scoped>
// 文本省略样式
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 卡片悬浮效果
.shadow-sm:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
</style>
