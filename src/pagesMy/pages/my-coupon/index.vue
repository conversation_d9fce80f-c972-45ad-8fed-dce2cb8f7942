<template>
  <z-paging
    ref="paging"
    v-model="couponList"
    @query="queryList"
    empty-view-text="暂无优惠券数据"
    class="bg-#fafafa"
  >
    <!-- 优惠券卡片列表 -->
    <view
      v-for="(coupon, index) in couponList"
      :key="coupon.id"
      class="mx-4 mt-4 relative"
      @click="handleCouponClick(coupon)"
    >
      <!-- 优惠券卡片 - 使用背景图片 -->
      <view
        class="relative w-full h-240rpx bg-cover bg-center bg-no-repeat"
        :style="{
          background: `url('${
            coupon.state === '2' || coupon.state === '3' || coupon.state === '4'
              ? '/pagesMy/static/isuse-border.png'
              : '/pagesMy/static/unuse-border.png'
          }')`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <!-- 优惠券内容区域 -->
        <view class="flex h-full px-2">
          <!-- 左侧优惠券面额区域 -->
          <view
            class="flex-shrink-0 w-220rpx flex flex-col items-center justify-center"
            :class="
              coupon.state === '2' || coupon.state === '3' || coupon.state === '4'
                ? 'text-#999999'
                : 'text-#244190'
            "
          >
            <!-- 优惠券面额 -->
            <view class="flex items-end justify-center">
              <text class="text-54rpx font-bold leading-none">
                {{ coupon.type == '3' ? coupon.discount : coupon.money }}
              </text>
              <text class="text-24rpx">
                {{ coupon.type == '3' ? `折` : '元' }}
              </text>
            </view>
            <text class="text-24rpx mt-1">
              {{ coupon.type == '2' ? `满${coupon.doorsill_money}可用` : '' }}
            </text>
          </view>

          <!-- 右侧优惠券信息区域 -->
          <view class="flex-1 py-4 flex flex-col justify-center relative">
            <view
              class="border-#E9E9E9 absolute w-2rpx top-15% left--3 h-70% border-dashed border-l border-b-none border-t-none border-r-none"
            ></view>
            <!-- 优惠券标题 -->
            <text
              class="text-36rpx font-medium mb-2 block leading-tight"
              :class="
                coupon.state === '2' || coupon.state === '3' || coupon.state === '4'
                  ? 'text-#999999'
                  : 'text-#244190'
              "
            >
              {{ couponTypeMap[coupon.type] }}
            </text>

            <!-- 适用场景 -->
            <text
              class="text-26rpx mb-2 block"
              :class="
                coupon.state === '2' || coupon.state === '3' || coupon.state === '4'
                  ? 'text-#999999'
                  : 'text-#244190'
              "
            >
              适用场景：{{ sceneTypeMap[coupon.scene] }}
            </text>

            <!-- 有效期 -->
            <text
              class="text-24rpx block"
              :class="
                coupon.state === '2' || coupon.state === '3' || coupon.state === '4'
                  ? 'text-#999999'
                  : 'text-#244190'
              "
            >
              有效期：{{ coupon.start_date }}至{{ coupon.end_date }}
            </text>
          </view>
        </view>

        <!-- 状态标记 -->
        <view
          v-if="coupon.state === '2' || coupon.state === '3' || coupon.state === '4'"
          class="absolute top-1/2 right-8 transform -translate-y-1/2 z-10"
        >
          <image :src="getStateImage(coupon.state)" class="w-160rpx h-160rpx" mode="aspectFit" />
        </view>
      </view>
    </view>
  </z-paging>

  <!-- 详情弹窗 -->
  <wd-overlay :show="overlayShow" @click="overlayShow = false">
    <view class="wrapper">
      <view class="block" @click.stop="">
        <view class="wrapper-title">
          {{ details.title || '优惠券详情' }}
        </view>
        <view>
          <view class="item-line">优惠券类型：{{ couponTypeMap[details.type] }}</view>
          <view class="item-line">
            优惠券面额：{{ details.type == '3' ? details.discount + '折' : details.money + '元' }}
          </view>
          <view class="item-line" v-if="details.type == '2'">
            使用门槛：满{{ details.doorsill_money }}可用
          </view>
          <view class="item-line">有效期：{{ details.start_date }}至{{ details.end_date }}</view>
          <view class="item-line" v-if="details.scene">
            适用场景：{{ sceneTypeMap[details.scene] }}
          </view>
          <view class="item-line">状态：{{ couponStatusMap[details.state] }}</view>
          <view class="item-line" v-if="details.description">说明：{{ details.description }}</view>
        </view>
      </view>
      <wd-icon name="close-outline" size="35px" :color="'#fff'"></wd-icon>
    </view>
  </wd-overlay>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getCouponList, sceneTypeMap, couponTypeMap, couponStatusMap } from '@/pagesMy/api/index.js'

// 分页组件引用
const paging = ref(null)
// 优惠券列表数据
const couponList = ref([])
// 详情弹窗相关
const overlayShow = ref(false)
const details = ref({})

// 获取优惠券背景图片   (1:未使用,2:已使用,3:已过期,4:已失效)
const getCouponBgImage = (state) => {
  if (state === '2' || state === '3' || state === '4') {
    return '/pagesMy/static/isuse-border.png'
  }
  return '/pagesMy/static/unuse-border.png'
}

// 获取状态图片
const getStateImage = (state) => {
  if (state === '2') {
    return '/pagesMy/static/is-use.png'
  } else if (state === '3' || state === '4') {
    return '/pagesMy/static/is-expired.png'
  }
  return ''
}

// 查询列表数据
const queryList = async (pageNo, pageSize) => {
  try {
    const res = await getCouponList({
      current: pageNo,
      size: pageSize,
    })
    const pageData = res.data.records || []
    paging.value.complete(pageData)
  } catch (error) {
    paging.value.complete(false)
  }
}

// 查看优惠券详情
const showCouponDetail = (coupon) => {
  details.value = coupon
  overlayShow.value = true
}

// 处理优惠券点击事件
const handleCouponClick = (coupon) => {
  // 直接显示详情，不区分状态
  showCouponDetail(coupon)
}
</script>

<style scoped lang="scss">
.wrapper {
  margin-top: 400rpx;
  text-align: center;
  .block {
    padding: 0 45rpx 42rpx;
    height: auto;
    margin: 0 30rpx;
    margin-bottom: 30rpx;
    min-height: 400rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    text-align: left;
    .wrapper-title {
      text-align: center;
      font-weight: bold;
      font-size: 32rpx;
      color: #333333;
      padding: 32rpx 0 36rpx;
    }
    .item-line {
      padding: 10rpx 0;
    }
  }
}
</style>
