import { request } from '/src/utils/request/index.js'
// 我的优惠券
export async function getCouponList(payload = {}) {
  return await request('/applet/coupon/myCoupon/getPage', { data: payload })
}

// 获取钱包余额
export async function getWalletBalance(payload = {}) {
  return await request('/applet/wallet/getBalance', { data: payload })
}

// 获取充值记录
export async function getRechargeRecords(payload = {}) {
  return await request('/applet/wallet/getRechargeRecords', { data: payload })
}

// 提交充值订单
export async function submitRecharge(payload = {}) {
  return await request('/applet/wallet/submitRecharge', { data: payload })
}

export const sceneTypeMap = {
  1: '线上订房',
  2: '购买商品',
  3: '购买套餐',
}
export const couponTypeMap = {
  1: '代金券',
  2: '满减券',
  3: '折扣券',
}

export const couponStatusMap = {
  1: '未使用',
  2: '已使用',
  3: '已过期',
  4: '已失效',
}

export async function getHotelOrderList(payload = {}) {
  return await request('/applet/orderManage/hotel/getPage', { data: payload })
}
export async function getMallOrderList(payload = {}) {
  return await request('/applet/orderManage/mall/getPage', { data: payload })
}
