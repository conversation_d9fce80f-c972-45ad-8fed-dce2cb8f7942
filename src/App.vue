<script>
import { useUser } from './utils/hook/useUser.js'
import { useDeploy } from './utils/hook/useDeploy.js'
const { initUserInfo } = useUser()
const { initPlatformDeployList } = useDeploy()
export default {
  onLaunch: function () {
    initUserInfo()
    initPlatformDeployList()
    // #ifdef MP-WEIXIN
    const updateManager = uni.getUpdateManager()
    updateManager.onUpdateReady(function (res) {
      uni.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        },
      })
    })

    updateManager.onUpdateFailed(function (res) {
      // 新的版本下载失败
    })

    // #endif
    console.log('App Launch')
  },
  onShow: function (e) {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
}
</script>

<style>
:root,
page {
  --wot-segmented-item-acitve-bg: #1285ff;
  --wot-segmented-item-bg-color: #f8f8f8;
  --wot-checkbox-bg: transparent;
  --wot-grid-item-padding: 24rpx 0;

  --wot-color-theme: #123f7f;
  --wot-swiper-radius: 0;

  --wot-sidebar-bg: #fefefe;
  --wot-sidebar-active-bg: #e4e4e4;

  --wot-cell-required-color: #244190;
}

page {
  background-color: #fafafa;
}
</style>
<style lang="scss">
.lg-flex {
  display: flex;
  flex-direction: row;
}

.lg-flea {
  display: flex;
  flex-direction: column;
}
.line1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
