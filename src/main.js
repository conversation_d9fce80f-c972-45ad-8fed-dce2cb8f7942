import { createSSRApp } from 'vue'
import App from './App.vue'
import directive from '/src/utils/directive.js'
import loginModel from '/src/components/login-model/index.vue'
import atOverlaySwiper from '/src/plugin/advertisement/advertisement-overlay-swiper.vue'
import atRecommend from '/src/plugin/advertisement/advertisement-recommend.vue'
import atSwiper from '/src/plugin/advertisement/advertisement-swiper.vue'
import wxadSwiper from '/src/plugin/advertisement/wxad-swiper.vue'
import lgApi from '/src/components/lg-api/lg-api.vue'
import 'uno.css'
export function createApp() {
  const app = createSSRApp(App)
  app.component('loginModel', loginModel)
  app.component('atOverlaySwiper', atOverlaySwiper)
  app.component('atRecommend', atRecommend)
  app.component('atSwiper', atSwiper)
  app.component('wxadSwiper', wxadSwiper)
  app.component('lgApi', lgApi)
  app.use(directive)
  return {
    app,
  }
}
