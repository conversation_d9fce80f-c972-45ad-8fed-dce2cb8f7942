function listToMap(list) {
    return list.reduce((acc, item) => {
        acc[item.value] = item.label;
        return acc;
    }, {});
}

// 优惠券类型
export const COUPON_TYPE_LIST = [
    { value: '3', label: '折扣券' },
    { value: '2', label: '满减券' },
    { value: '1', label: '代金券' }
];

export const COUPON_TYPE_MAP = listToMap(COUPON_TYPE_LIST);

// 范围
export const START_TYPE_LIST = [
    { value: false, label: '所有酒店' },
    { value: true, label: '自定义' }
];

// 适用场景 购买套餐 线上订房 购买商品
export const SCENE_TYPE_LIST = [
    { value: '1', label: '线上订房' },
    { value: '2', label: '购买商品' },
    { value: '3', label: '购买套餐' }
];

// 是否
export const IS_LIST = [
    { value: '1', label: '是' },
    { value: '2', label: '否' }
];

export const IS_MAP = listToMap(IS_LIST);

// 商品状态  1.待上架，2.在售，3，已下架，4，已售罄
export const GOODS_STATUS_LIST = [
    { value: '1', label: '待上架' },
    { value: '2', label: '在售' },
    { value: '3', label: '已下架' },
    { value: '4', label: '已售罄' }
];

export const GOODS_STATUS_MAP = listToMap(GOODS_STATUS_LIST);

// 配套设施 wifi 投影 空调 冰箱 多功能淋雨房
export const SERVICE_FACILITY_LIST = [
    { value: '1', label: 'WIFI' },
    { value: '2', label: '投影' },
    { value: '3', label: '空调' },
    { value: '4', label: '冰箱' },
    { value: '5', label: '多功能淋雨房' }
];

export const SERVICE_FACILITY_MAP = listToMap(SERVICE_FACILITY_LIST);

export const BANNER_LIST = [
    { value: '1', label: '首页-banner' },
    { value: '2', label: '首页-热门推荐' },
    { value: '3', label: '商城-barner' },
    { value: '4', label: '商城-好吃零食等你选' },
    { value: '5', label: '个人中心-VIP福利区' },
    { value: '6', label: '旅游主页' },
    { value: '7', label: '旅行日历主页' },
    { value: '8', label: '旅游日历-春/夏/秋/冬' }
];

export const BANNER_MAP = listToMap(BANNER_LIST);
