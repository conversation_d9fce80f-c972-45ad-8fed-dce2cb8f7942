@import './function';

/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
$-color-theme: var(--wot-color-theme, $default-theme) !default; // 主题色
$-color-white: var(--wot-color-white, rgb(255, 255, 255)) !default; // 用于mix的白色
$-color-black: var(--wot-color-black, rgb(0, 0, 0)) !default; // 用于mix的黑色

/* 辅助色 */
$-color-success: var(--wot-color-success, #34d19d) !default; // 成功色
$-color-warning: var(--wot-color-warning, #f0883a) !default; // 警告色
$-color-danger: var(--wot-color-danger, #fa4350) !default; // 危险出错色
$-color-purple: var(--wot-color-purple, #8268de) !default; // 紫色
$-color-yellow: var(--wot-color-yellow, #f0cd1d) !default; // 黄色
$-color-blue: var(--wot-color-blue, #2bb3ed) !default; // 蓝色
$-color-info: var(--wot-color-info, #909399) !default;

$-color-gray-1: var(--wot-color-gray-1, #f7f8fa) !default;
$-color-gray-2: var(--wot-color-gray-2, #f2f3f5) !default;
$-color-gray-3: var(--wot-color-gray-3, #ebedf0) !default;
$-color-gray-4: var(--wot-color-gray-4, #dcdee0) !default;
$-color-gray-5: var(--wot-color-gray-5, #c8c9cc) !default;
$-color-gray-6: var(--wot-color-gray-6, #969799) !default;
$-color-gray-7: var(--wot-color-gray-7, #646566) !default;
$-color-gray-8: var(--wot-color-gray-8, #323233) !default;

$-font-gray-1: var(--wot-font-gray-1, rgba(0, 0, 0, 0.9));
$-font-gray-2: var(--wot-font-gray-2, rgba(0, 0, 0, 0.6));
$-font-gray-3: var(--wot-font-gray-3, rgba(0, 0, 0, 0.4));
$-font-gray-4: var(--wot-font-gray-4, rgba(0, 0, 0, 0.26));

$-font-white-1: var(--wot-font-white-1, rgba(255, 255, 255, 1));
$-font-white-2: var(--wot-font-white-2, rgba(255, 255, 255, 0.55));
$-font-white-3: var(--wot-font-white-3, rgba(255, 255, 255, 0.35));
$-font-white-4: var(--wot-font-white-4, rgba(255, 255, 255, 0.22));

/* 文字颜色（默认浅色背景下 */
$-color-title: var(--wot-color-title, $-color-black) !default; // 模块标题/重要正文 000
$-color-content: var(--wot-color-content, #262626) !default; // 普通正文 262626
$-color-secondary: var(--wot-color-secondary, #595959) !default; // 次要信息，注释/补充/正文 595959
$-color-aid: var(--wot-color-aid, #8c8c8c) !default; // 辅助文字字号，弱化信息，引导性/不可点文字 8c8c8c
$-color-tip: var(--wot-color-tip, #bfbfbf) !default; // 失效、默认提示文字 bfbfbf
$-color-border: var(--wot-color-border, #d9d9d9) !default; // 控件边框线 d9d9d9
$-color-border-light: var(--wot-color-border-light, #e8e8e8) !default; // 分割线颜色 e8e8e8
$-color-bg: var(--wot-color-bg, #f5f5f5) !default; // 背景色、禁用填充色 f5f5f5

/* 暗黑模式 */
$-dark-background: var(--wot-dark-background, #131313) !default;
$-dark-background2: var(--wot-dark-background2, #1b1b1b) !default;
$-dark-background3: var(--wot-dark-background3, #141414) !default;
$-dark-background4: var(--wot-dark-background4, #323233) !default;
$-dark-background5: var(--wot-dark-background5, #646566) !default;
$-dark-background6: var(--wot-dark-background6, #380e08) !default;
$-dark-background7: var(--wot-dark-background7, #707070) !default;
$-dark-color: var(--wot-dark-color, $-color-white) !default;
$-dark-color2: var(--wot-dark-color2, #f2270c) !default;
$-dark-color3: var(--wot-dark-color3, rgba(232, 230, 227, 0.8)) !default;
$-dark-color-gray: var(--wot-dark-color-gray, $-color-secondary) !default;
$-dark-border-color: var(--wot-dark-border-color, #3a3a3c) !default;

/* 图形颜色 */
$-color-icon: var(--wot-color-icon, #d9d9d9) !default; // icon颜色
$-color-icon-active: var(--wot-color-icon-active, #eee) !default; // icon颜色hover
$-color-icon-disabled: var(--wot-color-icon-disabled, #a7a7a7) !default; // icon颜色disabled

/*----------------------------------------- Theme color. end -------------------------------------------*/

/*-------------------------------- Theme color application size.  start --------------------------------*/

/* 文字字号 */
$-fs-big: var(--wot-fs-big, 24px) !default; // 大型标题
$-fs-important: var(--wot-fs-important, 19px) !default; // 重要数据
$-fs-title: var(--wot-fs-title, 16px) !default; // 标题字号/重要正文字号
$-fs-content: var(--wot-fs-content, 14px) !default; // 普通正文
$-fs-secondary: var(--wot-fs-secondary, 12px) !default; // 次要信息，注释/补充/正文
$-fs-aid: var(--wot-fs-aid, 10px) !default; // 辅助文字字号，弱化信息，引导性/不可点文字

/* 文字字重 */
$-fw-medium: var(--wot-fw-medium, 500) !default; // PingFangSC-Medium
$-fw-semibold: var(--wot-fw-semibold, 600) !default; // PingFangSC-Semibold

/* 尺寸 */
$-size-side-padding: var(--wot-size-side-padding, 15px) !default; // 屏幕两边留白

/*-------------------------------- Theme color application size.  end --------------------------------*/

/* component var */

/* action-sheet */
$-action-sheet-weight: var(--wot-action-sheet-weight, 500) !default; // 面板字重
$-action-sheet-radius: var(--wot-action-sheet-radius, 16px) !default; // 面板圆角大小
$-action-sheet-loading-size: var(--wot-action-sheet-loading-size, 20px) !default; // loading动画尺寸
$-action-sheet-action-height: var(--wot-action-sheet-action-height, 48px) !default; // 单条菜单高度
$-action-sheet-color: var(--wot-action-sheet-color, rgba(0, 0, 0, 0.85)) !default; // 选项名称颜色
$-action-sheet-fs: var(--wot-action-sheet-fs, $-fs-title) !default; // 选项名称字号
$-action-sheet-active-color: var(--wot-action-sheet-active-color, $-color-bg) !default; // 点击高亮颜色
$-action-sheet-subname-fs: var(--wot-action-sheet-subname-fs, $-fs-secondary) !default; // 描述信息字号
$-action-sheet-subname-color: var(--wot-action-sheet-subname-color, rgba(0, 0, 0, 0.45)) !default; // 描述信息颜色
$-action-sheet-disabled-color: var(--wot-action-sheet-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用颜色
$-action-sheet-bg: var(--wot-action-sheet-bg, $-color-white) !default; // 菜单容器颜色（取消按钮上方的颜色）
$-action-sheet-title-height: var(--wot-action-sheet-title-height, 64px) !default; // 标题高度
$-action-sheet-title-fs: var(--wot-action-sheet-title-fs, $-fs-title) !default; // 标题字号
$-action-sheet-close-fs: var(--wot-action-sheet-close-fs, $-fs-title) !default; // 关闭按钮大小
$-action-sheet-close-color: var(--wot-action-sheet-close-color, rgba(0, 0, 0, 0.65)) !default; // 关闭按钮颜色
$-action-sheet-close-top: var(--wot-action-sheet-close-top, 25px) !default; // 关闭按钮距离标题顶部距离
$-action-sheet-close-right: var(--wot-action-sheet-close-right, 15px) !default; // 关闭按钮距离标题右侧距离
$-action-sheet-cancel-color: var(--wot-action-sheet-cancel-color, #131415) !default; // 取消按钮颜色
$-action-sheet-cancel-height: var(--wot-action-sheet-cancel-height, 44px) !default; // 取消按钮高度
$-action-sheet-cancel-bg: var(--wot-action-sheet-cancel-bg, rgba(240, 240, 240, 1)) !default; // 取消按钮背景色
$-action-sheet-cancel-radius: var(--wot-action-sheet-cancel-radius, 22px) !default; // 取消按钮圆角大小
$-action-sheet-panel-padding: var(--wot-action-sheet-panel-padding, 12px 0 11px) !default; // 自定义面板内边距大小
$-action-sheet-panel-img-fs: var(--wot-action-sheet-panel-img-fs, 40px) !default; // 自定义面板图片大小
$-action-sheet-panel-img-radius: var(--wot-action-sheet-panel-img-radius, 4px) !default; // 自定义面板图片圆角大小

/* badge */
$-badge-bg: var(--wot-badge-bg, $-color-danger) !default; // 背景填充颜色
$-badge-color: var(--wot-badge-color, #fff) !default; // 文字颜色
$-badge-fs: var(--wot-badge-fs, 12px) !default; // 文字字号
$-badge-padding: var(--wot-badge-padding, 0 5px) !default; // padding
$-badge-height: var(--wot-badge-height, 16px) !default; // 高度
$-badge-primary: var(--wot-badge-primary, $-color-theme) !default;
$-badge-success: var(--wot-badge-success, $-color-success) !default;
$-badge-warning: var(--wot-badge-warning, $-color-warning) !default;
$-badge-danger: var(--wot-badge-danger, $-color-danger) !default;
$-badge-info: var(--wot-badge-info, $-color-info) !default;
$-badge-dot-size: var(--wot-badge-dot-size, 6px) !default; // dot 类型大小
$-badge-border: var(--wot-badge-border, 2px solid $-badge-color) !default; // 边框样式

/* button */
$-button-disabled-opacity: var(--wot-button-disabled-opacity, 0.6) !default; // button禁用透明度
$-button-small-height: var(--wot-button-small-height, 28px) !default; // 小型按钮高度
$-button-small-padding: var(--wot-button-small-padding, 0 12px) !default; // 小型按钮padding
$-button-small-fs: var(--wot-button-small-fs, $-fs-secondary) !default; // 小型按钮字号
$-button-small-radius: var(--wot-button-small-radius, 2px) !default; // 小型按钮圆角大小
$-button-small-loading: var(--wot-button-small-loading, 14px) !default; // 小型按钮loading图标大小
$-button-medium-height: var(--wot-button-medium-height, 36px) !default; // 中型按钮高度
$-button-medium-padding: var(--wot-button-medium-padding, 0 16px) !default; // 中型按钮padding
$-button-medium-fs: var(--wot-button-medium-fs, $-fs-content) !default; // 中型按钮字号
$-button-medium-radius: var(--wot-button-medium-radius, 4px) !default; // 中型按钮圆角大小
$-button-medium-loading: var(--wot-button-medium-loading, 18px) !default; // 中型按钮loading图标大小
$-button-medium-box-shadow-size: var(--wot-button-medium-box-shadow-size, 0px 2px 4px 0px) !default; // 中尺寸阴影尺寸
$-button-large-height: var(--wot-button-large-height, 44px) !default; // 大型按钮高度
$-button-large-padding: var(--wot-button-large-padding, 0 36px) !default; // 大型按钮padding
$-button-large-fs: var(--wot-button-large-fs, $-fs-title) !default; // 大型按钮字号
$-button-large-radius: var(--wot-button-large-radius, 8px) !default; // 大型按钮圆角大小
$-button-large-loading: var(--wot-button-large-loading, 24px) !default; // 大小按钮loading图标大小
$-button-large-box-shadow-size: var(--wot-button-large-box-shadow-size, 0px 4px 8px 0px) !default; // 大尺寸阴影尺寸
$-button-icon-fs: var(--wot-button-icon-fs, 1.18em) !default; // 带图标的按钮的图标大小
$-button-icon-size: var(--wot-button-icon-size, 40px) !default; // icon 类型按钮尺寸
$-button-icon-color: var(--wot-button-icon-color, rgba(0, 0, 0, 0.65)) !default; // icon 类型按钮颜色
$-button-icon-disabled-color: var(--wot-button-icon-disabled-color, $-color-icon-disabled) !default; // icon 类型按钮禁用颜色
$-button-normal-color: var(--wot-button-normal-color, $-color-title) !default; // 文字颜色
$-button-normal-disabled-color: var(--wot-button-normal-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 默认按钮禁用文字色
$-button-plain-bg-color: var(--wot-button-plain-bg-color, $-color-white) !default; // 幽灵按钮背景色
$-button-primary-color: var(--wot-button-primary-color, $-color-white) !default; // 主要按钮颜色
$-button-primary-bg-color: var(--wot-button-primary-bg-color, $-color-theme) !default; // 主要按钮背景颜色
$-button-success-color: var(--wot-button-success-color, $-color-white) !default; // 成功按钮文字颜色
$-button-success-bg-color: var(--wot-button-success-bg-color, $-color-success) !default; // 成功按钮颜色
$-button-info-color: var(--wot-button-info-color, $-color-title) !default; // 信息按钮颜色
$-button-info-bg-color: var(--wot-button-info-bg-color, #f0f0f0) !default; // 信息按钮背景颜色
$-button-info-plain-border-color: var(--wot-button-info-plain-border-color, rgba(0, 0, 0, 0.45)) !default; // 信息按钮禁用颜色
$-button-info-plain-normal-color: var(--wot-button-info-plain-normal-color, rgba(0, 0, 0, 0.85)) !default; // 信息幽灵按钮默认颜色
$-button-warning-color: var(--wot-button-warning-color, $-color-white) !default; // 警告按钮字体颜色
$-button-warning-bg-color: var(--wot-button-warning-bg-color, $-color-warning) !default; // 警告按钮背景颜色
$-button-error-color: var(--wot-button-error-color, $-color-white) !default; // 错误按钮颜色
$-button-error-bg-color: var(--wot-button-error-bg-color, $-color-danger) !default; // 错误按钮背景颜色
$-button-text-hover-opacity: var(--wot-button-text-hover-opacity, 0.7) !default; // 文字button激活时透明度

/* cell */
$-cell-padding: var(--wot-cell-padding, $-size-side-padding) !default; // cell 左右padding距离
$-cell-line-height: var(--wot-cell-line-height, 24px) !default; // 行高

$-cell-group-title-fs: var(--wot-cell-group-title-fs, $-fs-title) !default; // 组标题字号
$-cell-group-padding: var(--wot-cell-group-padding, 13px $-cell-padding) !default; // 组padding
$-cell-group-title-color: var(--wot-cell-group-title-color, rgba(0, 0, 0, 0.85)) !default; // 组标题文字颜色
$-cell-group-value-fs: var(--wot-cell-group-value-fs, $-fs-content) !default; // 组值字号
$-cell-group-value-color: var(--wot-cell-group-value-color, $-color-content) !default; // 组值文字颜色

$-cell-wrapper-padding: var(--wot-cell-wrapper-padding, 10px) !default; // cell 容器padding
$-cell-wrapper-padding-large: var(--wot-cell-wrapper-padding-large, 12px) !default; // large类型cell容器padding

$-cell-wrapper-padding-with-label: var(--wot-cell-wrapper-padding-with-label, 16px) !default; // cell 容器上下padding（有label情况下）
$-cell-icon-right: var(--wot-cell-icon-right, 4px) !default; // 图标距离右边缘
$-cell-icon-size: var(--wot-cell-icon-size, 16px) !default; // 图标大小
$-cell-title-fs: var(--wot-cell-title-fs, 14px) !default; // 标题字号
$-cell-title-color: var(--wot-cell-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题文字颜色
$-cell-label-fs: var(--wot-cell-label-fs, 12px) !default; // 描述信息字号
$-cell-label-color: var(--wot-cell-label-color, rgba(0, 0, 0, 0.45)) !default; // 描述信息文字颜色
$-cell-value-fs: var(--wot-cell-value-fs, 14px) !default; // 右侧内容字号
$-cell-value-color: var(--wot-cell-value-color, rgba(0, 0, 0, 0.85)) !default; // 右侧内容文字颜色
$-cell-arrow-size: var(--wot-cell-arrow-size, 18px) !default; // 右箭头大小
$-cell-arrow-color: var(--wot-cell-arrow-color, rgba(0, 0, 0, 0.25)) !default; // 右箭头颜色
$-cell-clear-color: var(--wot-cell-clear-color, #585858) !default; // 清空按钮颜色
$-cell-tap-bg: var(--wot-cell-tap-bg, rgba(0, 0, 0, 0.06)) !default; // 点击态背景色

$-cell-title-fs-large: var(--wot-cell-title-fs-large, 16px) !default; // 大尺寸标题字号
$-cell-label-fs-large: var(--wot-cell-label-fs-large, 14px) !default; // 描述信息字号
$-cell-icon-size-large: var(--wot-cell-icon-size-large, 18px) !default; // 图标大小

$-cell-required-color: var(--wot-cell-required-color, $-color-danger) !default; // 要求必填*颜色
$-cell-required-size: var(--wot-cell-required-size, 18px) !default; // 必填*字号
$-cell-vertical-top: var(--wot-cell-vertical-top, 16px) !default; // 表单类型-上下结构的间距

/* calendar */
$-calendar-fs: var(--wot-calendar-fs, 16px) !default;
$-calendar-panel-padding: var(--wot-calendar-panel-padding, 0 12px) !default;
$-calendar-panel-title-fs: var(--wot-calendar-panel-title-fs, 14px) !default;
$-calendar-panel-title-color: var(--wot-calendar-panel-title-color, rgba(0, 0, 0, 0.85)) !default;
$-calendar-week-color: var(--wot-calendar-week-color, rgba(0, 0, 0, 0.85)) !default;
$-calendar-week-height: var(--wot-calendar-week-height, 36px) !default;
$-calendar-week-fs: var(--wot-calendar-week-fs, 12px) !default;
$-calendar-day-fs: var(--wot-calendar-day-fs, 16px) !default;
$-calendar-day-color: var(--wot-calendar-day-color, rgba(0, 0, 0, 0.85)) !default;
$-calendar-day-fw: var(--wot-calendar-day-fw, 500) !default;
$-calendar-day-height: var(--wot-calendar-day-height, 64px) !default;
$-calendar-month-width: var(--wot-calendar-month-width, 50px) !default;
$-calendar-active-color: var(--wot-calendar-active-color, $-color-theme) !default;
$-calendar-selected-color: var(--wot-calendar-selected-color, $-color-white) !default;
$-calendar-disabled-color: var(--wot-calendar-disabled-color, rgba(0, 0, 0, 0.25)) !default;
$-calendar-range-color: var(--wot-calendar-range-color, rgba(#4d80f0, 0.09)) !default;
$-calendar-active-border: var(--wot-calendar-active-border, 8px) !default;
$-calendar-info-fs: var(--wot-calendar-info-fs, 10px) !default;
$-calendar-item-margin-bottom: var(--wot-calendar-item-margin-bottom, 4px) !default;
 

/* checkbox */
$-checkbox-margin: var(--wot-checkbox-margin, 10px) !default; // 多个复选框距离
$-checkbox-bg: var(--wot-checkbox-bg, $-color-white) !default; // 多个复选框距离
$-checkbox-label-margin: var(--wot-checkbox-label-margin, 9px) !default; // 右侧文字与左侧图标距离
$-checkbox-size: var(--wot-checkbox-size, 16px) !default; // 左侧图标尺寸
$-checkbox-icon-size: var(--wot-checkbox-icon-size, 14px) !default; // 左侧图标尺寸
$-checkbox-border-color: var(--wot-checkbox-border-color, #dcdcdc) !default; // 左侧图标边框颜色
$-checkbox-check-color: var(--wot-checkbox-check-color, $-color-white) !default; // 左侧图标边框颜色
$-checkbox-label-fs: var(--wot-checkbox-label-fs, 14px) !default; // 右侧文字字号
$-checkbox-label-color: var(--wot-checkbox-label-color, rgba(0, 0, 0, 0.85)) !default; // 右侧文字颜色
$-checkbox-checked-color: var(--wot-checkbox-checked-color, $-color-theme) !default; // 选中颜色

$-checkbox-disabled-color: var(--wot-checkbox-disabled-color, rgba(0, 0, 0, 0.04)) !default; // 禁用背景颜色
$-checkbox-disabled-label-color: var(--wot-checkbox-disabled-label-color, rgba(0, 0, 0, 0.25)) !default; // 禁用文字颜色
$-checkbox-disabled-check-color: var(--wot-checkbox-disabled-check-color, rgba(0, 0, 0, 0.15)) !default; // 禁用图标颜色
$-checkbox-disabled-check-bg: var(--wot-checkbox-disabled-check-bg, rgba(0, 0, 0, 0.15)) !default; // 禁用边框背景颜色
$-checkbox-square-radius: var(--wot-checkbox-square-radius, 4px) !default; // 方型圆角大小

$-checkbox-large-size: var(--wot-checkbox-large-size, 18px) !default; // 左侧图标尺寸
$-checkbox-large-label-fs: var(--wot-checkbox-large-label-fs, 16px) !default; // 右侧文字字号

$-checkbox-button-height: var(--wot-checkbox-button-height, 32px) !default; // 按钮模式复选框高
$-checkbox-button-min-width: var(--wot-checkbox-button-min-width, 78px) !default; // 按钮模式最小宽
$-checkbox-button-radius: var(--wot-checkbox-button-radius, 16px) !default; // 按钮圆角大小
$-checkbox-button-bg: var(--wot-checkbox-button-bg, rgba(0, 0, 0, 0.04)) !default; // 按钮模式背景颜色
$-checkbox-button-font-size: var(--wot-checkbox-button-font-size, 14px) !default; // 按钮模式字号
$-checkbox-button-border: var(--wot-checkbox-button-border, #f5f5f5) !default; // 按钮边框颜色
$-checkbox-button-disabled-border: var(--wot-checkbox-button-disabled-border, rgba(0, 0, 0, 0.15)) !default; // 按钮禁用边框颜色

/* collapse */
$-collapse-side-padding: var(--wot-collapse-side-padding, $-size-side-padding) !default; // 左右间距
$-collapse-body-padding: var(--wot-collapse-body-padding, 14px $-size-side-padding) !default; // body padding
$-collapse-header-padding: var(--wot-collapse-header-padding, 13px $-size-side-padding) !default; // 头部padding
$-collapse-title-color: var(--wot-collapse-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题颜色
$-collapse-title-fs: var(--wot-collapse-title-fs, 16px) !default; // 标题字号
$-collapse-arrow-size: var(--wot-collapse-arrow-size, 18px) !default; // 箭头大小
$-collapse-arrow-color: var(--wot-collapse-arrow-color, #d8d8d8) !default; // 箭头颜色
$-collapse-body-fs: var(--wot-collapse-body-fs, 14px) !default; // 内容字号
$-collapse-body-color: var(--wot-collapse-body-color, rgba(0, 0, 0, 0.65)) !default; // 内容颜色
$-collapse-disabled-color: var(--wot-collapse-disabled-color, rgba(0, 0, 0, 0.15)) !default; // 禁用颜色
$-collapse-retract-fs: var(--wot-collapse-retract-fs, 14px) !default; // 更多 字号
$-collapse-more-color: var(--wot-collapse-more-color, $-color-theme) !default; // 更多 颜色

/* divider */
$-divider-padding: var(--wot-divider-padding, 0 $-size-side-padding) !default; // 两边间距
$-divider-margin: var(--wot-divider-margin, 16px 0) !default; // 上下间距
$-divider-color: var(--wot-divider-color, rgba(0, 0, 0, 0.45)) !default; // 字体颜色
$-divider-line-color: var(--wot-divider-line-color, currentColor) !default; // 线条颜色
$-divider-line-height: var(--wot-divider-line-height, 1px) !default; // 线条高度
$-divider-fs: var(--wot-divider-fs, 14px) !default; // 字体大小
$-divider-content-left-width: var(--wot-divider-content-left-width, 10%) !default; // 左侧内容宽度
$-divider-content-left-margin: var(--wot-divider-content-left-margin, 12px) !default; // 左侧内容距离线距离
$-divider-content-right-margin: var(--wot-divider-content-right-margin, 12px) !default; // 右侧内容距离线距离
$-divider-content-right-width: var(--wot-divider-content-right-width, 10%) !default; // 右侧内容宽度
$-divider-vertical-height: var(--wot-divider-vertical-height, 16px) !default; // 垂直分割线高度
$-divider-vertical-content-margin: var(--wot-divider-vertical-content-margin, 0 8px) !default; // 垂直分割线内容间距
$-divider-vertical-line-width: var(--wot-divider-vertical-line-width, 1px) !default; // 线条高度




/* drop-menu */
$-drop-menu-height: var(--wot-drop-menu-height, 48px) !default; // 展示选中项的高度
$-drop-menu-color: var(--wot-drop-menu-color, $-color-content) !default; // 展示选中项的颜色
$-drop-menu-fs: var(--wot-drop-menu-fs, $-fs-content) !default; // 展示选中项的字号
$-drop-menu-arrow-fs: var(--wot-drop-menu-arrow-fs, $-fs-content) !default; // 箭头图标大小

$-drop-menu-side-padding: var(--wot-drop-menu-side-padding, $-size-side-padding) !default; // 两边留白间距
$-drop-menu-disabled-color: var(--wot-drop-menu-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用颜色
$-drop-menu-item-height: var(--wot-drop-menu-item-height, 48px) !default; // 选项高度
$-drop-menu-item-color: var(--wot-drop-menu-item-color, $-color-content) !default; // 选项颜色
$-drop-menu-item-fs: var(--wot-drop-menu-item-fs, $-fs-content) !default; // 选项字号
$-drop-menu-item-color-active: var(--wot-drop-menu-item-color-active, $-color-theme) !default; // 选中颜色
$-drop-menu-item-color-tip: var(--wot-drop-menu-item-color-tip, rgba(0, 0, 0, 0.45)) !default; // 提示文字颜色
$-drop-menu-item-fs-tip: var(--wot-drop-menu-item-fs-tip, $-fs-secondary) !default; // 提示文字字号
$-drop-menu-option-check-size: var(--wot-drop-menu-option-check-size, 20px) !default; // check 图标大小
$-drop-menu-line-color: var(--wot-drop-menu-line-color, $-color-theme) !default; // 下划线颜色
$-drop-menu-line-height: var(--wot-drop-menu-line-height, 3px) !default; // 下划线高度

/* input-number */
$-input-number-color: var(--wot-input-number-color, #262626) !default; // 文字颜色
$-input-number-border-color: var(--wot-input-number-border-color, #e8e8e8) !default; // 边框颜色
$-input-number-disabled-color: var(--wot-input-number-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用颜色
$-input-number-height: var(--wot-input-number-height, 24px) !default; // 加减号按钮高度
$-input-number-btn-width: var(--wot-input-number-btn-width, 26px) !default; // 加减号按钮宽度
$-input-number-input-width: var(--wot-input-number-input-width, 36px) !default; // 输入框宽度
$-input-number-radius: var(--wot-input-number-radius, 4px) !default; // 加减号按钮圆角大小
$-input-number-fs: var(--wot-input-number-fs, 12px) !default; // 输入框字号
$-input-number-icon-size: var(--wot-input-number-icon-size, 14px) !default; // 加减号图标大小
$-input-number-icon-color: var(--wot-input-number-icon-color, rgba(0, 0, 0, 0.65)) !default; // icon颜色

/* input */
$-input-padding: var(--wot-input-padding, $-size-side-padding) !default; // input 左右padding距离
$-input-border-color: var(--wot-input-border-color, #dadada) !default; // 无label边框颜色
$-input-not-empty-border-color: var(--wot-input-not-empty-border-color, #262626) !default; // 输入框有值时 无label边框颜色
$-input-fs: var(--wot-input-fs, $-cell-title-fs) !default; // 字号
$-input-fs-large: var(--wot-input-fs-large, $-cell-title-fs-large) !default; // 大尺寸字号
$-input-icon-margin: var(--wot-input-icon-margin, 8px) !default; // 图标距离
$-input-color: var(--wot-input-color, #262626) !default; // 文字颜色
$-input-placeholder-color: var(--wot-input-placeholder-color, #bfbfbf) !default; // 占位符颜色
$-input-disabled-color: var(--wot-input-disabled-color, #d9d9d9) !default; // 输入框禁用颜色
$-input-error-color: var(--wot-input-error-color, $-color-danger) !default; // 输入框错误颜色
$-input-icon-color: var(--wot-input-icon-color, #bfbfbf) !default; // 图标颜色
$-input-clear-color: var(--wot-input-clear-color, #585858) !default; // 关闭按钮颜色
$-input-count-color: var(--wot-input-count-color, #bfbfbf) !default; // 计数文字颜色
$-input-count-current-color: var(--wot-input-count-current-color, #262626) !default; // 当前长度颜色
$-input-bg: var(--wot-input-bg, $-color-white) !default; // 默认背景颜色

$-input-cell-bg: var(--wot-input-cell-bg, $-color-white) !default; // cell 类型背景色
$-input-cell-border-color: var(--wot-input-cell-border-color, $-color-border-light) !default; // cell 类型边框颜色
$-input-cell-padding: var(--wot-input-cell-padding, 10px) !default; // cell 容器padding
$-input-cell-padding-large: var(--wot-input-cell-padding-large, 12px) !default; // large类型cell容器padding
$-input-cell-height: var(--wot-input-cell-height, 24px) !default; // cell 高度
$-input-cell-label-width: var(--wot-input-cell-label-width, 33%) !default; // cell 下 label 的宽度
$-input-inner-height: var(--wot-input-inner-height, 34px) !default; // 非cell和textarea下的高度
$-input-inner-height-no-border: var(--wot-input-inner-height-no-border, 24px) !default; // 无边框下的高度
$-input-count-fs: var(--wot-input-count-fs, 14px) !default; // 计数字号
$-input-count-fs-large: var(--wot-input-count-fs-large, 14px) !default; // 大尺寸计数字号
$-input-icon-size: var(--wot-input-icon-size, 16px) !default; // 图标大小
$-input-icon-size-large: var(--wot-input-icon-size-large, 18px) !default; // 大尺寸图标大小

/* textarea */
$-textarea-padding: var(--wot-textarea-padding, $-size-side-padding) !default; // textarea 左右padding距离
$-textarea-border-color: var(--wot-textarea-border-color, #dadada) !default; // 无label边框颜色
$-textarea-not-empty-border-color: var(--wot-textarea-not-empty-border-color, #262626) !default; // 输入框有值时 无label边框颜色
$-textarea-fs: var(--wot-textarea-fs, $-cell-title-fs) !default; // 字号
$-textarea-fs-large: var(--wot-textarea-fs-large, $-cell-title-fs-large) !default; // 大尺寸字号
$-textarea-icon-margin: var(--wot-textarea-icon-margin, 8px) !default; // 图标距离
$-textarea-color: var(--wot-textarea-color, #262626) !default; // 文字颜色
$-textarea-icon-color: var(--wot-textarea-icon-color, #bfbfbf) !default; // 图标颜色
$-textarea-clear-color: var(--wot-textarea-clear-color, #585858) !default; // 关闭按钮颜色
$-textarea-count-color: var(--wot-textarea-count-color, #bfbfbf) !default; // 计数文字颜色
$-textarea-count-current-color: var(--wot-textarea-count-current-color, #262626) !default; // 当前长度颜色
$-textarea-bg: var(--wot-textarea-bg, $-color-white) !default; // 默认背景颜色
$-textarea-cell-border-color: var(--wot-textarea-cell-border-color, $-color-border-light) !default; // cell 类型边框颜色
$-textarea-cell-padding: var(--wot-textarea-cell-padding, 10px) !default; // cell 容器padding
$-textarea-cell-padding-large: var(--wot-textarea-cell-padding-large, 12px) !default; // large类型cell容器padding
$-textarea-cell-height: var(--wot-textarea-cell-height, 24px) !default; // cell 高度
$-textarea-count-fs: var(--wot-textarea-count-fs, 14px) !default; // 计数字号
$-textarea-count-fs-large: var(--wot-textarea-count-fs-large, 14px) !default; // 大尺寸计数字号
$-textarea-icon-size: var(--wot-textarea-icon-size, 16px) !default; // 图标大小
$-textarea-icon-size-large: var(--wot-textarea-icon-size-large, 18px) !default; // 大尺寸图标大小

/* loadmore */
$-loadmore-height: var(--wot-loadmore-height, 48px) !default; // 高度
$-loadmore-color: var(--wot-loadmore-color, rgba(0, 0, 0, 0.45)) !default; // 颜色
$-loadmore-fs: var(--wot-loadmore-fs, 14px) !default; // 字号
$-loadmore-error-color: var(--wot-loadmore-error-color, $-color-theme) !default; // 点击重试颜色
$-loadmore-refresh-fs: var(--wot-loadmore-refresh-fs, $-fs-title) !default; // refresh图标字号
$-loadmore-loading-size: var(--wot-loadmore-loading-size, $-fs-title) !default; // loading尺寸

/* message-box */
$-message-box-width: var(--wot-message-box-width, 300px) !default; // 宽度
$-message-box-bg: var(--wot-message-box-bg, $-color-white) !default; // 默认背景颜色
$-message-box-radius: var(--wot-message-box-radius, 16px) !default; // 圆角大小
$-message-box-padding: var(--wot-message-box-padding, 25px 24px 0) !default; // 主体内容padding
$-message-box-title-fs: var(--wot-message-box-title-fs, 16px) !default; // 标题字号
$-message-box-title-color: var(--wot-message-box-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题颜色
$-message-box-content-fs: var(--wot-message-box-content-fs, 14px) !default; // 内容字号
$-message-box-content-color: var(--wot-message-box-content-color, #666666) !default; // 内容颜色
$-message-box-content-max-height: var(--wot-message-box-content-max-height, 264px) !default; // 内容最大高度
$-message-box-content-scrollbar-width: var(--wot-message-box-content-scrollbar-width, 4px) !default; // 内容滚动条宽度
$-message-box-content-scrollbar-color: var(--wot-message-box-content-scrollbar-color, rgba(0, 0, 0, 0.1)) !default; // 内容滚动条颜色
$-message-box-input-error-color: var(--wot-message-box-input-error-color, $-input-error-color) !default; // 输入框错误颜色

/* notice-bar */
$-notice-bar-fs: var(--wot-notice-bar-fs, 12px) !default; // 字号
$-notice-bar-line-height: var(--wot-notice-bar-line-height, 18px) !default; // 行高
$-notice-bar-border-radius: var(--wot-notice-bar-border-radius, 8px) !default; // 圆角
$-notice-bar-padding: var(--wot-notice-bar-padding, 9px 20px 9px 15px) !default; // 非换行下的padding
$-notice-bar-warning-bg: var(--wot-notice-bar-warning-bg, #fff6c8) !default; // 背景色
$-notice-bar-info-bg: var(--wot-notice-bar-info-bg, #f4f9ff) !default; // 背景色
$-notice-bar-danger-bg: var(--wot-notice-bar-danger-bg, #feeced) !default; // 背景色
$-notice-bar-warning-color: var(--wot-notice-bar-warning-color, $-color-warning) !default; // 文字和图标颜色
$-notice-bar-info-color: var(--wot-notice-bar-info-color, $-color-theme) !default; // 文字和图标颜色
$-notice-bar-danger-color: var(--wot-notice-bar-danger-color, $-color-danger) !default; // 文字和图标颜色
$-notice-bar-prefix-size: var(--wot-notice-bar-prefix-size, 18px) !default; // 图标大小
$-notice-bar-close-bg: var(--wot-notice-bar-close-bg, rgba(0, 0, 0, 0.15)) !default; // 右侧关闭按钮背景颜色
$-notice-bar-close-size: var(--wot-notice-bar-close-size, 18px) !default; // 右侧关闭按钮背景颜色
$-notice-bar-close-color: var(--wot-notice-bar-close-color, $-color-white) !default; // 右侧关闭按钮颜色
$-notice-bar-wrap-padding: var(--wot-notice-bar-wrap-padding, 14px $-size-side-padding) !default; // 换行下的padding

/* pagination */
$-pagination-content-padding: var(--wot-pagination-content-padding, 10px 15px) !default;
$-pagination-message-padding: var(--wot-pagination-message-padding, 1px 0 16px 0) !default;
$-pagination-message-fs: var(--wot-pagination-message-fs, 12px) !default;
$-pagination-message-color: var(--wot-pagination-message-color, rgba(0, 0, 0, 0.69)) !default;
$-pagination-nav-border: var(--wot-pagination-nav-border, 1px solid rgba(0, 0, 0, 0.45)) !default;
$-pagination-nav-border-radius: var(--wot-pagination-nav-border-radius, 16px) !default;
$-pagination-nav-fs: var(--wot-pagination-nav-fs, 12px) !default;
$-pagination-nav-width: var(--wot-pagination-nav-width, 60px) !default;
$-pagination-nav-color: var(--wot-pagination-nav-color, rgba(0, 0, 0, 0.85)) !default;
$-pagination-nav-content-fs: var(--wot-pagination-nav-content-fs, 12px) !default;
$-pagination-nav-sepatator-padding: var(--wot-pagination-nav-sepatator-padding, 0 4px) !default;
$-pagination-nav-current-color: var(--wot-pagination-nav-current-color, $-color-theme) !default;
$-pagination-icon-size: var(--wot-pagination-icon-size, $-fs-content) !default;

/* picker */
$-picker-toolbar-height: var(--wot-picker-toolbar-height, 54px) !default; // toolbar 操作条的高度
$-picker-action-height: var(--wot-picker-action-height, 16px) !default; // toolbar 操作条的高度
$-picker-toolbar-finish-color: var(--wot-picker-toolbar-finish-color, $-color-theme) !default; // toolbar 操作条完成按钮的颜色
$-picker-toolbar-cancel-color: var(--wot-picker-toolbar-cancel-color, #666666) !default; // toolbar 操作条的边框颜色
$-picker-toolbar-fs: var(--wot-picker-toolbar-fs, $-fs-title) !default; // toolbar 操作条的字号
$-picker-toolbar-title-color: var(--wot-picker-toolbar-title-color, rgba(0, 0, 0, 0.85)) !default; // toolbar 操作台的标题颜色
$-picker-column-fs: var(--wot-picker-column-fs, 16px) !default; // 选择器选项的字号
$-picker-bg: var(--wot-picker-bg, $-color-white) !default; // 选择器选项的字号
$-picker-column-active-fs: var(--wot-picker-column-active-fs, 18px) !default; // 选择器选项被选中的字号
$-picker-column-color: var(--wot-picker-column-color, rgba(0, 0, 0, 0.85)) !default; // 选择器选项的颜色
$-picker-column-height: var(--wot-picker-column-height, 210px) !default; // 列高 滚筒外部的高度
$-picker-column-item-height: var(--wot-picker-column-item-height, 35px) !default; // 列高 滚筒外部的高度
$-picker-column-select-bg: var(--wot-picker-column-select-bg, #f5f5f5) !default;
$-picker-loading-button-color: var(--wot-picker-loading-button-color, rgba(0, 0, 0, 0.25)) !default; // loading 背景颜色
$-picker-column-padding: var(--wot-picker-column-padding, 0 $-size-side-padding) !default; // 选项内间距

$-picker-column-disabled-color: var(--wot-picker-column-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 选择器选项禁用的颜色
$-picker-mask: var(--wot-picker-mask, linear-gradient(180deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.25)))
  linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.25)) !default; // 上下阴影
$-picker-loading-bg: var(--wot-picker-loading-bg, rgba($-color-white, 0.8)) !default; // loading 背景颜色
$-picker-region-separator-color: var(--wot-picker-region-separator-color, rgba(0, 0, 0, 0.65)) !default; // 区域选择文字颜色
$-picker-cell-arrow-size-large: var(--wot-picker-cell-arrow-size-large, $-cell-icon-size) !default; // cell 类型的大尺寸 右侧icon尺寸

$-picker-region-color: var(--wot-picker-region-color, rgba(0, 0, 0, 0.45)) !default; // 区域选择文字颜色
$-picker-region-bg-active-color: var(--wot-picker-region-bg-active-color, $-color-theme) !default; // 区域选择激活选中背景颜色

$-picker-region-fs: var(--wot-picker-region-fs, 14px) !default; // 区域选择文字字号

/* col-picker */
$-col-picker-selected-height: var(--wot-col-picker-selected-height, 44px) !default; // 弹框顶部值高度
$-col-picker-selected-padding: var(--wot-col-picker-selected-padding, 0 16px) !default; // 弹框顶部值左右间距
$-col-picker-selected-fs: var(--wot-col-picker-selected-fs, 14px) !default; // 弹框顶部值字号
$-col-picker-selected-color: var(--wot-col-picker-selected-color, rgba(0, 0, 0, 0.85)) !default; // 弹框顶部值文字颜色
$-col-picker-selected-fw: var(--wot-col-picker-selected-fw, 700) !default; // 弹框顶部值高亮字重
$-col-picker-line-width: var(--wot-col-picker-line-width, 16px) !default; // 弹框顶部值高亮线条宽度
$-col-picker-line-height: var(--wot-col-picker-line-height, 3px) !default; // 弹框顶部值高亮线条高度
$-col-picker-line-color: var(
  --wot-col-picker-line-color,
  linear-gradient(315deg, rgba(81, 124, 240, 1), rgba(118, 158, 245, 1))
) !default; // 弹框顶部值高亮线条颜色
$-col-picker-line-box-shadow: var(--wot-col-picker-line-box-shadow, 0px 1px 2px 0px rgba(1, 87, 255, 0.2)) !default; // 弹框顶部值高亮线条阴影
$-col-picker-list-height: var(--wot-col-picker-list-height, 53vh) !default; // 弹框列表高度
$-col-picker-list-padding-bottom: var(--wot-col-picker-list-padding-bottom, 30px) !default; // 弹框列表底部间距
$-col-picker-list-color: var(--wot-col-picker-list-color, rgba(0, 0, 0, 0.85)) !default; // 弹框列表文字颜色
$-col-picker-list-color-disabled: var(--wot-col-picker-list-color-disabled, rgba(0, 0, 0, 0.15)) !default; // 弹框列表文字禁用颜色
$-col-picker-list-color-tip: var(--wot-col-picker-list-color-tip, rgba(0, 0, 0, 0.45)) !default; // 弹框列表提示文字颜色
$-col-picker-list-fs: var(--wot-col-picker-list-fs, 14px) !default; // 弹框列表文字字号
$-col-picker-list-fs-tip: var(--wot-col-picker-list-fs-tip, 12px) !default; // 弹框列表提示文字字号
$-col-picker-list-item-padding: var(--wot-col-picker-list-item-padding, 12px 15px) !default; // 弹框列表选项间距
$-col-picker-list-checked-icon-size: var(--wot-col-picker-list-checked-icon-size, 18px) !default; // 弹框列表选中箭头大小
$-col-picker-list-color-checked: var(--wot-col-picker-list-color-checked, $-color-theme) !default; // 弹框列表选中选项颜色

/* overlay */
$-overlay-bg: var(--wot-overlay-bg, rgba(0, 0, 0, 0.65)) !default;
$-overlay-bg-dark: var(--wot-overlay-bg-dark, rgba(0, 0, 0, 0.75)) !default;

/* popup */
$-popup-close-size: var(--wot-popup-close-size, 24px) !default; // 关闭按钮尺寸
$-popup-close-color: var(--wot-popup-close-color, #666) !default; // 关闭按钮颜色

/* progress */
$-progress-padding: var(--wot-progress-padding, 9px 0 8px) !default; // 进度条内边距
$-progress-bg: var(--wot-progress-bg, rgba(229, 229, 229, 1)) !default; // 进度条底色
$-progress-danger-color: var(--wot-progress-danger-color, $-color-danger) !default; // 进度条danger颜色
$-progress-success-color: var(--wot-progress-success-color, $-color-success) !default; // 进度条success进度条颜色
$-progress-warning-color: var(--wot-progress-warning-color, $-color-warning) !default; // 进度条warning进度条颜色

$-progress-color: var(--wot-progress-color, $-color-theme) !default; // 进度条颜色
$-progress-height: var(--wot-progress-height, 3px) !default; // 进度条高度
$-progress-label-color: var(--wot-progress-label-color, #333) !default; // 文字颜色
$-progress-label-fs: var(--wot-progress-label-fs, 14px) !default; // 文字字号
$-progress-icon-fs: var(--wot-progress-icon-fs, 18px) !default; // 图标字号

/* radio */
$-radio-margin: var(--wot-radio-margin, $-checkbox-margin) !default; // 多个单选框距离
$-radio-label-margin: var(--wot-radio-label-margin, $-checkbox-label-margin) !default; // 右侧文字与左侧图标距离
$-radio-size: var(--wot-radio-size, 16px) !default; // 左侧图标尺寸
$-radio-bg: var(--wot-radio-bg, $-color-white) !default; // 左侧图标尺寸
$-radio-label-fs: var(--wot-radio-label-fs, $-checkbox-label-fs) !default; // 右侧文字字号
$-radio-label-color: var(--wot-radio-label-color, $-checkbox-label-color) !default; // 右侧文字颜色
$-radio-checked-color: var(--wot-radio-checked-color, $-checkbox-checked-color) !default; // 选中颜色
$-radio-disabled-color: var(--wot-radio-disabled-color, $-checkbox-disabled-color) !default; // 禁用颜色
$-radio-disabled-label-color: var(--wot-radio-disabled-label-color, $-checkbox-disabled-label-color) !default; // 禁用文字颜色

$-radio-large-size: var(--wot-radio-large-size, $-checkbox-large-size) !default; // 左侧图标尺寸
$-radio-large-label-fs: var(--wot-radio-large-label-fs, $-checkbox-large-label-fs) !default; // 右侧文字字号

$-radio-button-height: var(--wot-radio-button-height, $-checkbox-button-height) !default; // 按钮模式复选框高
$-radio-button-min-width: var(--wot-radio-button-min-width, 60px) !default; // 按钮模式最小宽
$-radio-button-max-width: var(--wot-radio-button-max-width, 144px) !default; // 按钮模式最大宽
$-radio-button-radius: var(--wot-radio-button-radius, $-checkbox-button-radius) !default; // 按钮圆角大小
$-radio-button-bg: var(--wot-radio-button-bg, $-checkbox-button-bg) !default; // 按钮模式背景颜色
$-radio-button-fs: var(--wot-radio-button-fs, $-checkbox-button-font-size) !default; // 按钮模式字号
$-radio-button-border: var(--wot-radio-button-border, $-checkbox-button-border) !default; // 按钮边框颜色
$-radio-button-disabled-border: var(--wot-radio-button-disabled-border, $-checkbox-button-disabled-border) !default; // 按钮禁用边框颜色

$-radio-dot-size: var(--wot-radio-dot-size, 8px) !default; // 单选dot模式圆点尺寸
$-radio-dot-large-size: var(--wot-radio-dot-large-size, 10px) !default; // 单选dot模式大尺寸圆点尺寸
$-radio-dot-checked-bg: var(--wot-radio-dot-checked-bg, $-color-theme) !default; // 单选dot模式选中背景色
$-radio-dot-checked-border-color: var(--wot-radio-dot-checked-border-color, $-color-theme) !default; // 单选dot模式选中边框色
$-radio-dot-border-color: var(--wot-radio-dot-border-color, #dcdcdc) !default; // 单选dot模式边框色
$-radio-dot-disabled-border: var(--wot-radio-dot-disabled-border, #d9d9d9) !default; // 单选dot模式禁用边框颜色
$-radio-dot-disabled-bg: var(--wot-radio-dot-disabled-bg, #d9d9d9) !default; // 单选dot模式禁用背景颜色

/* search */
$-search-side-padding: var(--wot-search-side-padding, $-size-side-padding) !default; // 左右间距
$-search-padding: var(--wot-search-padding, 10px 0 10px $-search-side-padding) !default; // 不包含取消按钮的间距
$-search-input-radius: var(--wot-search-input-radius, 15px) !default; // 输入框圆角大小
$-search-input-bg: var(--wot-search-input-bg, $-color-bg) !default; // 输入框背景色
$-search-input-height: var(--wot-search-input-height, 30px) !default; // 输入框高度
$-search-input-padding: var(--wot-search-input-padding, 0 32px 0 42px) !default; // 输入框间距
$-search-input-fs: var(--wot-search-input-fs, $-fs-content) !default; // 输入框字号
$-search-input-color: var(--wot-search-input-color, #262626) !default; // 输入框文字颜色
$-search-icon-color: var(--wot-search-icon-color, $-color-icon) !default; // 图标颜色
$-search-icon-size: var(--wot-search-icon-size, 18px) !default; // 图标大小
$-search-clear-icon-size: var(--wot-search-clear-icon-size, $-fs-title) !default; // 清除图标大小
$-search-placeholder-color: var(--wot-search-placeholder-color, #bfbfbf) !default; // placeholder 颜色
$-search-cancel-padding: var(--wot-search-cancel-padding, 0 $-search-side-padding 0 10px) !default; // 取消按钮间距
$-search-cancel-fs: var(--wot-search-cancel-fs, $-fs-title) !default; // 取消按钮字号
$-search-cancel-color: var(--wot-search-cancel-color, rgba(0, 0, 0, 0.65)) !default; // 取消按钮颜色
$-search-light-bg: var(--wot-search-light-bg, $-color-bg) !default; // light 类型的容器背景色

/* slider */
$-slider-fs: var(--wot-slider-fs, $-fs-content) !default; // 字体大小
$-slider-handle-radius: var(--wot-slider-handle-radius, 12px) !default; // 滑块半径
$-slider-handle-bg: var(--wot-slider-handle-bg, resultColor(139deg, $-color-theme, 'dark' 'light', #ffffff #f7f7f7, 0% 100%)) !default; // 滑块背景
$-slider-axie-height: var(--wot-slider-axie-height, 3px) !default; // 滑轴高度
$-slider-color: var(--wot-slider-color, #333) !default; // 字体颜色
$-slider-axie-bg: var(--wot-slider-axie-bg, #e5e5e5) !default; // 滑轴的默认背景色
$-slider-line-color: var(
  --wot-slider-line-color,
  resultColor(315deg, $-color-theme, 'dark' 'light', #517cf0 #769ef5, 0% 100%)
) !default; // 进度条颜色
$-slider-disabled-color: var(--wot-slider-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用状态下字体颜色

/* sort-button */
$-sort-button-fs: var(--wot-sort-button-fs, $-fs-content) !default; // 字号
$-sort-button-color: var(--wot-sort-button-color, $-color-content) !default; // 颜色
$-sort-button-height: var(--wot-sort-button-height, 48px) !default; // 高度
$-sort-button-line-height: var(--wot-sort-button-line-height, 3px) !default; // 下划线高度
$-sort-button-line-color: var(--wot-sort-button-line-color, $-color-theme) !default; // 下划线颜色

/* steps */
$-steps-icon-size: var(--wot-steps-icon-size, 22px) !default; // 图标尺寸
$-steps-inactive-color: var(--wot-steps-inactive-color, rgba(0, 0, 0, 0.25)) !default; // 等待状态文字颜色
$-steps-finished-color: var(--wot-steps-finished-color, $-color-theme) !default; // 完成文字颜色
$-steps-icon-text-fs: var(--wot-steps-icon-text-fs, $-fs-content) !default; // 数字图标文字字号
$-steps-error-color: var(--wot-steps-error-color, $-color-danger) !default; // 异常颜色
$-steps-title-fs: var(--wot-steps-title-fs, $-fs-content) !default; // 标题字号
$-steps-title-fw: var(--wot-steps-title-fw, $-fw-medium) !default; // 标题字重
$-steps-label-fs: var(--wot-steps-label-fs, $-fs-secondary) !default; // 描述信息字号
$-steps-description-color: var(--wot-steps-description-color, rgba(0, 0, 0, 0.45)) !default; // 描述信息颜色
$-steps-is-icon-width: var(--wot-steps-is-icon-width, 30px) !default; // 自定义图标的宽度，给左右留白
$-steps-line-color: var(--wot-steps-line-color, rgba(0, 0, 0, 0.15)) !default; // 线条颜色
$-steps-dot-size: var(--wot-steps-dot-size, 7px) !default; // 点状大小
$-steps-dot-active-size: var(--wot-steps-dot-active-size, 9px) !default; // 点状高亮大小

/* switch */
$-switch-size: var(--wot-switch-size, 28px) !default; // switch大小
$-switch-width: var(--wot-switch-width, calc(1.8em + 4px)) !default; // 宽度
$-switch-height: var(--wot-switch-height, calc(1em + 4px)) !default; // 高度
$-switch-circle-size: var(--wot-switch-circle-size, 1em) !default; // 圆点大小
$-switch-border-color: var(--wot-switch-border-color, #e5e5e5) !default; // 边框颜色选中状态背景颜色
$-switch-active-color: var(--wot-switch-active-color, $-color-theme) !default; // 选中状态背景
$-switch-active-shadow-color: var(--wot-switch-active-shadow-color, rgba(0, 83, 162, 0.5)) !default; // 选中状态shadow颜色
$-switch-inactive-color: var(--wot-switch-inactive-color, #eaeaea) !default; // 非选中背景颜色
$-switch-inactive-shadow-color: var(--wot-switch-inactive-shadow-color, rgba(155, 155, 155, 0.5)) !default; // 非选中状态shadow颜色

/* tabs */
$-tabs-nav-arrow-fs: var(--wot-tabs-nav-arrow-fs, 18px) !default; // 全部Icon字号
$-tabs-nav-arrow-open-fs: var(--wot-tabs-nav-arrow-open-fs, 14px) !default; // 展开Icon字号
$-tabs-nav-width: var(--wot-tabs-nav-width, 100vw) !default; // tabs 头部切换宽度
$-tabs-nav-height: var(--wot-tabs-nav-height, 42px) !default; // 头部切换高度
$-tabs-nav-fs: var(--wot-tabs-nav-fs, $-fs-content) !default; // 头部切换文字大小
$-tabs-nav-color: var(--wot-tabs-nav-color, rgba(0, 0, 0, 0.85)) !default; // 头部切换文字颜色
$-tabs-nav-bg: var(--wot-tabs-nav-bg, $-color-white) !default; // 背景颜色
$-tabs-nav-active-color: var(--wot-tabs-nav-active-color, $-color-theme) !default; // 头部高亮颜色
$-tabs-nav-disabled-color: var(--wot-tabs-nav-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 头部禁用颜色
$-tabs-nav-line-height: var(--wot-tabs-nav-line-height, 3px) !default; // 高亮边框高度
$-tabs-nav-line-width: var(--wot-tabs-nav-line-width, 19px) !default; // 高亮边框宽度
$-tabs-nav-line-bg-color: var(--wot-tabs-nav-line-bg-color, $-color-theme) !default; // 底部条颜色
$-tabs-nav-map-fs: var(--wot-tabs-nav-map-fs, $-fs-content) !default; // map 类型按钮字号
$-tabs-nav-map-color: var(--wot-tabs-nav-map-color, rgba(0, 0, 0, 0.85)) !default; // map 类型按钮文字颜色
$-tabs-nav-map-arrow-color: var(--wot-tabs-nav-map-arrow-color, rgba(0, 0, 0, 0.65)) !default; // map 类型箭头颜色
$-tabs-nav-map-btn-before-bg: var(
  --wot-tabs-nav-map-btn-before-bg,
  linear-gradient(270deg, rgba(255, 255, 255, 1) 1%, rgba(255, 255, 255, 0) 100%)
) !default; // 左侧map遮罩阴影
$-tabs-nav-map-button-back-color: var(--wot-tabs-nav-map-button-back-color, rgba(0, 0, 0, 0.04)) !default; // map 类型按钮边框颜色
$-tabs-nav-map-button-radius: var(--wot-tabs-nav-map-button-radius, 16px) !default; // map 类型按钮圆角大小
$-tabs-nav-map-modal-bg: var(--wot-tabs-nav-map-modal-bg, $-overlay-bg) !default; // map 类型蒙层背景色

/* tag */
$-tag-fs: var(--wot-tag-fs, $-fs-secondary) !default; // 字号
$-tag-color: var(--wot-tag-color, $-color-white) !default; // 字体颜色
$-tag-small-fs: var(--wot-tag-small-fs, $-fs-aid) !default; // 小尺寸字号
$-tag-info-color: var(--wot-tag-info-color, #585858) !default; // info 颜色
$-tag-primary-color: var(--wot-tag-primary-color, $-color-theme) !default; // 主颜色
$-tag-danger-color: var(--wot-tag-danger-color, $-color-danger) !default; // danger 颜色
$-tag-warning-color: var(--wot-tag-warning-color, $-color-warning) !default; // warning 颜色
$-tag-success-color: var(--wot-tag-success-color, $-color-success) !default; // success 颜色
$-tag-info-bg: var(--wot-tag-info-bg, resultColor(49deg, $-color-black, 'dark' 'light', #808080 #999999, 0% 100%)) !default; // info 背景颜色
$-tag-primary-bg: var(--wot-tag-primary-bg, $-color-theme) !default; // 主背景颜色
$-tag-danger-bg: var(--wot-tag-danger-bg, $-color-danger) !default; // danger 背景颜色
$-tag-warning-bg: var(--wot-tag-warning-bg, $-color-warning) !default; // warning 背景颜色
$-tag-success-bg: var(--wot-tag-success-bg, $-color-success) !default; // success 背景颜色
$-tag-round-color: var(--wot-tag-round-color, rgba(102, 102, 102, 1)) !default; // round 字体颜色
$-tag-round-border-color: var(--wot-tag-round-border-color, rgba(225, 225, 225, 1)) !default; // round 边框颜色
$-tag-round-radius: var(--wot-tag-round-radius, 12px) !default; // round 圆角大小
$-tag-mark-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px) !default; // mark 圆角大小
$-tag-close-size: var(--wot-tag-close-size, 14px) !default; // 关闭按钮字号
$-tag-close-color: var(--wot-tag-close-color, $-tag-info-color) !default; // 关闭按钮颜色
$-tag-close-active-color: var(--wot-tag-close-active-color, rgba(0, 0, 0, 0.45)) !default; // 关闭按钮 active 颜色

/* toast */
$-toast-padding: var(--wot-toast-padding, 16px 24px) !default; // padding
$-toast-max-width: var(--wot-toast-max-width, 300px) !default; // 最大宽度
$-toast-radius: var(--wot-toast-radius, 8px) !default; // 圆角大小
$-toast-bg: var(--wot-toast-bg, $-overlay-bg) !default; // 背景色
$-toast-fs: var(--wot-toast-fs, $-fs-content) !default; // 字号
$-toast-with-icon-min-width: var(--wot-toast-with-icon-min-width, 150px) !default; // 有图标的情况下最小宽度
$-toast-icon-size: var(--wot-toast-icon-size, 32px) !default; // 图标大小
$-toast-icon-margin-right: var(--wot-toast-icon-margin-right, 12px) !default; // 图标右边距
$-toast-icon-margin-bottom: var(--wot-toast-icon-margin-bottom, 12px) !default; // 图标下边距
$-toast-loading-padding: var(--wot-toast-loading-padding, 10px) !default; // loading 下的padding
$-toast-box-shadow: var(--wot-toast-box-shadow, 0px 6px 16px 0px rgba(0, 0, 0, 0.08)) !default; // 外部阴影

/* loading */
$-loading-size: var(--wot-loading-size, 32px) !default; // loading 大小

/* tooltip */
$-tooltip-bg: var(--wot-tooltip-bg, rgba(38, 39, 40, 0.8)) !default; // 背景色
$-tooltip-color: var(--wot-tooltip-color, $-color-white) !default; // 文字颜色
$-tooltip-radius: var(--wot-tooltip-radius, 8px) !default; // 圆角大小
$-tooltip-arrow-size: var(--wot-tooltip-arrow-size, 5px) !default; // 箭头大小
$-tooltip-fs: var(--wot-tooltip-fs, $-fs-content) !default; // 字号
$-tooltip-blur: var(--wot-tooltip-blur, 10px) !default; // 背景高斯模糊效果
$-tooltip-padding: var(--wot-tooltip-padding, 9px 20px) !default; // 间距
$-tooltip-close-size: var(--wot-tooltip-close-size, 6px) !default; // 背景高斯模糊效果
$-tooltip-z-index: var(--wot-tooltip-z-index, 500) !default;
$-tooltip-line-height: var(--wot-tooltip-line-height, 18px) !default; // 行高

/* popover */
$-popover-bg: var(--wot-popover-bg, $-color-white) !default; // 背景色
$-popover-color: var(--wot-popover-color, rgba(0, 0, 0, 0.85)) !default; // 文字颜色
$-popover-box-shadow: var(--wot-popover-box-shadow, 0px 2px 10px 0px rgba(0, 0, 0, 0.1)) !default; // 阴影颜色
$-popover-arrow-box-shadow: var(--wot-popover-arrow-box-shadow, 0px 2px 10px 0px rgba(0, 0, 0, 0.2)) !default; // 阴影颜色
$-popover-border-color: var(--wot-popover-border-color, rgba(0, 0, 0, 0.09)) !default; // 阴影颜色
$-popover-radius: var(--wot-popover-radius, 4px) !default; // 圆角大小
$-popover-arrow-size: var(--wot-popover-arrow-size, 6px) !default; // 箭头大小
$-popover-fs: var(--wot-popover-fs, $-fs-content) !default; // 字号
$-popover-padding: var(--wot-popover-padding, 15px) !default; // 间距
$-popover-line-height: var(--wot-popover-line-height, 18px) !default; // 行高
$-popover-z-index: var(--wot-popover-z-index, $-tooltip-z-index) !default;

/* grid-item */
$-grid-item-fs: var(--wot-grid-item-fs, 12px) !default; // 字号
$-grid-item-bg: var(--wot-grid-item-bg, $-color-white) !default; // 字号
$-grid-item-padding: var(--wot-grid-item-padding, 14px 0px) !default; // 内容的 padding
$-grid-item-border-color: var(--wot-grid-item-border-color, $-color-border-light) !default; // 边框颜色
$-grid-item-hover-bg: var(--wot-grid-item-hover-bg, $-color-gray-3) !default; // hover背景色
$-grid-item-hover-bg-dark: var(--wot-grid-item-hover-bg-dark, $-color-gray-7) !default; // 暗黑模式hover背景色

/* statustip */
$-statustip-fs: var(--wot-statustip-fs, $-fs-content) !default; // 字号
$-statustip-color: var(--wot-statustip-color, rgba(0, 0, 0, 0.45)) !default; // 文字颜色
$-statustip-line-height: var(--wot-statustip-line-height, 16px) !default; // 文字行高
$-statustip-padding: var(--wot-statustip-padding, 5px 10px) !default; // 间距

/* card */
$-card-bg: var(--wot-card-bg, $-color-white) !default; // 背景色
$-card-fs: var(--wot-card-fs, $-fs-content) !default; // 卡片字号
$-card-padding: var(--wot-card-padding, 0 $-size-side-padding) !default; // 内边距
$-card-footer-padding: var(--wot-card-footer-padding, 12px 0 16px) !default; // 底部内边距
$-card-shadow-color: var(--wot-card-shadow-color, 0px 4px 8px 0px rgba(0, 0, 0, 0.02)) !default; // 阴影
$-card-radius: var(--wot-card-radius, 8px) !default; // 圆角大小
$-card-line-height: var(--wot-card-line-height, 1.1) !default; // 行高
$-card-margin: var(--wot-card-margin, 0 $-size-side-padding) !default; // 外边距
$-card-title-color: var(--wot-card-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题颜色
$-card-title-fs: var(--wot-card-title-fs, $-fs-title) !default; //  矩形卡片标题字号
$-card-content-border-color: var(--wot-card-content-border-color, rgba(0, 0, 0, 0.09)) !default; // 内容边框
$-card-rectangle-title-padding: var(--wot-card-rectangle-title-padding, 15px 15px 12px) !default; // 矩形卡片头部内边距
$-card-rectangle-content-padding: var(--wot-card-rectangle-content-padding, 16px 0) !default; // 矩形卡片内容内边距
$-card-rectangle-footer-padding: var(--wot-card-rectangle-footer-padding, 12px 0) !default; // 矩形卡片底部内边距
$-card-content-color: var(--wot-card-content-color, rgba(0, 0, 0, 0.45)) !default; // 文本内容颜色
$-card-content-line-height: var(--wot-card-content-line-height, 1.428) !default; // 文本内容行高
$-card-content-margin: var(--wot-card-content-margin, 13px 0 12px) !default; // 内容外边距
$-card-content-rectangle-margin: var(--wot-card-content-rectangle-margin, 14px 0 12px) !default; // 矩形卡片内容外边距

/* upload */
$-upload-size: var(--wot-upload-size, 80px) !default; // upload的外边框默认尺寸
$-upload-evoke-icon-size: var(--wot-upload-evoke-icon-size, 32px) !default; // 唤起项的图标大小
$-upload-evoke-bg: var(--wot-upload-evoke-bg, rgba(0, 0, 0, 0.04)) !default; // 唤起项的背景色
$-upload-evoke-color: var(--wot-upload-evoke-color, rgba(0, 0, 0, 0.25)) !default; // 唤起项的图标颜色
$-upload-evoke-disabled-color: var(--wot-upload-evoke-disabled-color, rgba(0, 0, 0, 0.09)) !default; // 唤起项禁用颜色
$-upload-close-icon-size: var(--wot-upload-close-icon-size, 16px) !default; // 移除按钮尺寸
$-upload-close-icon-color: var(--wot-upload-close-icon-color, rgba(0, 0, 0, 0.65)) !default; // 移除按钮颜色
$-upload-progress-fs: var(--wot-upload-progress-fs, 14px) !default; // 进度文字字号
$-upload-file-fs: var(--wot-upload-file-fs, 12px) !default; // 文件名字号
$-upload-file-color: var(--wot-upload-file-color, $-color-secondary) !default; // 文件名字颜色
$-upload-preview-name-fs: var(--wot-upload-preview-name-fs, 12px) !default; // 预览图片名字号
$-upload-preview-icon-size: var(--wot-upload-preview-icon-size, 24px) !default; // 预览内部图标尺寸
$-upload-preview-name-bg: var(--wot-upload-preview-name-bg, rgba(0, 0, 0, 0.6)) !default; // 预览文件名背景色
$-upload-preview-name-height: var(--wot-upload-preview-name-height, 22px) !default; // 预览文件名背景高度
$-upload-cover-icon-size: var(--wot-upload-cover-icon-size, 22px) !default; // 视频/文件图标尺寸

/* curtain */
$-curtain-content-radius: var(--wot-curtain-content-radius, 24px) !default; // 内容圆角
$-curtain-content-close-color: var(--wot-curtain-content-close-color, $-color-white) !default; // 关闭按钮颜色
$-curtain-content-close-fs: var(--wot-curtain-content-close-fs, $-fs-big) !default; // 关闭按钮大小

/* notify */
$-notify-text-color: var(--wot-notify-text-color, $-color-white) !default;
$-notify-padding: var(--wot-notify-padding, 8px 16px) !default;
$-notify-font-size: var(--wot-notify-font-size, $-fs-content) !default;
$-notify-line-height: var(--wot-notify-line-height, 20px) !default;
$-notify-primary-background: var(--wot-notify-primary-background, $-color-theme) !default;
$-notify-success-background: var(--wot-notify-success-background, $-color-success) !default;
$-notify-danger-background: var(--wot-notify-danger-background, $-color-danger) !default;
$-notify-warning-background: var(--wot-notify-warning-background, $-color-warning) !default;

/* skeleton */
$-skeleton-background-color: var(--wot-skeleton-background-color, #eee) !default;
$-skeleton-animation-gradient: var(--wot-skeleton-animation-gradient, rgba(0, 0, 0, 0.04)) !default;
$-skeleton-animation-flashed: var(--wot-skeleton-animation-flashed, rgba(230, 230, 230, 0.3)) !default;
$-skeleton-text-height-default: var(--wot-skeleton-text-height-default, 16px) !default;
$-skeleton-rect-height-default: var(--wot-skeleton-rect-height-default, 16px) !default;
$-skeleton-circle-height-default: var(--wot-skeleton-circle-height-default, 48px) !default;
$-skeleton-row-margin-bottom: var(--wot-skeleton-row-margin-bottom, 16px) !default;
$-skeleton-border-radius-text: var(--wot-skeleton-border-radius-text, 2px) !default;
$-skeleton-border-radius-rect: var(--wot-skeleton-border-radius-rect, 4px) !default;
$-skeleton-border-radius-circle: var(--wot-skeleton-border-radius-circle, 50%) !default;

/* circle */
$-circle-text-color: var(--wot-circle-text-color, $-color-content) !default; // circle文字颜色

/* swiper */
$-swiper-radius: var(--wot-swiper-radius, 8px);
$-swiper-item-padding: var(--wot-swiper-item-padding, 0);
$-swiper-item-text-color: var(--wot-swiper-item-text-color, #ffffff);
$-swiper-item-text-fs: var(--wot-swiper-item-text-fs, $-fs-title);


/* swiper-nav */
// dot & dots-bar
$-swiper-nav-dot-color: var(--wot-swiper-nav-dot-color, $-font-white-2) !default;
$-swiper-nav-dot-active-color: var(--wot-swiper-nav-dot-active-color, $-font-white-1) !default;
$-swiper-nav-dot-size: var(--wot-swiper-nav-dot-size, 12rpx) !default;
$-swiper-nav-dots-bar-active-width: var(--wot-swiper-nav-dots-bar-active-width, 40rpx) !default;
// fraction
$-swiper-nav-fraction-color: var(--wot-swiper-nav-fraction-color, $-font-white-1) !default;
$-swiper-nav-fraction-bg-color: var(--wot-swiper-nav-fraction-bg-color, $-font-gray-3) !default;
$-swiper-nav-fraction-height: var(--wot-swiper-nav-fraction-height, 48rpx) !default;
$-swiper-nav-fraction-font-size: var(--wot-swiper-nav-fraction-font-size, 24rpx) !default;
// button
$-swiper-nav-btn-color: var(--wot-swiper-nav-btn-color, $-font-white-1) !default;
$-swiper-nav-btn-bg-color: var(--wot-swiper-nav-btn-bg-color, $-font-gray-3) !default;
$-swiper-nav-btn-size: var(--wot-swiper-nav-btn-size, 48rpx) !default;

/* segmented */
$-segmented-padding: var(--wot-segmented-padding, 4px) !default; // 分段器padding
$-segmented-item-bg-color: var(--wot-segmented-item-bg-color, #eeeeee) !default;
$-segmented-item-color: var(--wot-segmented-item-color, rgba(0, 0, 0, 0.85)) !default; // 标题文字颜色
$-segmented-item-acitve-bg: var(--wot-segmented-item-acitve-bg, #ffffff) !default; // 标题文字颜色
$-segmented-item-disabled-color: var(--wot-segmented-item-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 标题文字禁用颜色

/* tabbar */
$-tabbar-height: var(--wot-tabbar-height, 50px) !default;
$-tabbar-box-shadow: var(
  --wot-tabbar-box-shadow,
  0 6px 30px 5px rgba(0, 0, 0, 0.05),
  0 16px 24px 2px rgba(0, 0, 0, 0.04),
  0 8px 10px -5px rgba(0, 0, 0, 0.08)
) !default; // round类型tabbar阴影

/* tabbar-item */
$-tabbar-item-title-font-size: var(--wot-tabbar-item-title-font-size, 10px) !default; // tabbar选项文字大小
$-tabbar-item-title-line-height: var(--wot-tabbar-item-title-line-height, initial) !default; // tabbar选项标题文字行高
$-tabbar-inactive-color: var(--wot-tabbar-inactive-color, $-color-title) !default; // 标题文字和图标颜色
$-tabbar-active-color: var(--wot-tabbar-active-color, $-color-theme) !default; // 选中文字和图标颜色
$-tabbar-item-icon-size: var(--wot-tabbar-item-icon-size, 20px) !default; // tabbar选项图标大小

/* navbar */
$-navbar-height: var(--wot-navbar-height, 44px) !default; // navbar高度
$-navbar-color: var(--wot-navbar-color, $-font-gray-1) !default; // navbar字体颜色
$-navbar-background: var(--wot-navbar-background, $-color-white) !default; // navbar背景颜色
$-navbar-arrow-size: var(--wot-navbar-arrow-size, 24px) !default; // navbar左箭头图标大小
$-navbar-desc-font-size: var(--wot-navbar-desc-font-size, 16px); // navbar 左箭头字体大小
$-navbar-desc-font-color: var(--wot-navbar-desc-font-color, $-font-gray-1) !default; // navbar左右两侧字体颜色
$-navbar-title-font-size: var(--wot-navbar-title-font-size, 18px); // navbar title字体大小
$-navbar-title-font-weight: var(--wot-navbar-title-font-weight, 600); // navbar title字重
$-navbar-disabled-opacity: var(--wot-navbar-disabled-opacity, 0.6) !default; // navbar左右两侧字体禁用
$-navbar-hover-color: var(--wot-navbar-hover-color, #eee) !default; // navbar hover样式

/* navbar-capsule */
$-navbar-capsule-border-color: var(--wot-navbar-capsule-border-color, #e7e7e7) !default;
$-navbar-capsule-border-radius: var(--wot-navbar-capsule-border-radius, 16px) !default;
$-navbar-capsule-width: var(--wot-navbar-capsule-width, 88px) !default;
$-navbar-capsule-height: var(--wot-navbar-capsule-height, 32px) !default;
$-navbar-capsule-icon-size: var(--wot-navbar-capsule-icon-size, 20px) !default; // navbar capsule图标大小

/* table */
$-table-color: var(--wot-table-color, $-font-gray-1) !default; // 表格字体颜色
$-table-bg: var(--wot-table-bg, #ffffff) !default; // 表格背景颜色
$-table-stripe-bg: var(--wot-table-stripe-bg, #f3f3f3) !default; // 表格背景颜色
$-table-border-color: var(--wot-table-border-color, #ececec) !default; // 表格边框颜色
$-table-font-size: var(--wot-table-font-size, 13px) !default; // 表格字体大小

/* sidebar */
$-sidebar-bg: var(--wot-sidebar-bg, $-color-gray-1) !default; // 侧边栏背景色
$-sidebar-width: var(--wot-sidebar-width, 104px) !default; // 侧边栏宽度
$-sidebar-height: var(--wot-sidebar-height, 100%) !default; // 侧边栏高度

/* sidebar-item */
$-sidebar-color: var(--wot-sidebar-color, $-font-gray-1) !default;
$-sidebar-item-height: var(--wot-sidebar-item-height, 56px) !default;
$-sidebar-item-line-height: var(--wot-sidebar-item-line-height, 24px) !default;
$-sidebar-disabled-color: var(--wot-side-bar-disabled-color, $-font-gray-4) !default;
$-sidebar-active-color: var(--wot-sidebar-active-color, $-color-theme) !default; // 激活项字体颜色
$-sidebar-active-bg: var(--wot-sidebar-active-bg, $-color-white) !default; // 激活项背景颜色
$-sidebar-hover-bg: var(--wot-sidebar-hover-bg, $-color-gray-2) !default; // 激活项点击背景颜色
$-sidebar-border-radius: var(--wot-sidebar-border-radius, 8px) !default;
$-sidebar-font-size: var(--wot-sidebar-font-size, 16px) !default;
$-sidebar-icon-size: var(--wot-sidebar-icon-size, 20px) !default;
$-sidebar-active-border-width: var(--wot-sidebar-active-border-width, 4px) !default;
$-sidebar-active-border-height: var(--wot-sidebar-active-border-height, 16px) !default;

/* fab */
$-fab-trigger-height: var(--wot-fab-trigger-height, 56px) !default;
$-fab-trigger-width: var(--wot-fab-trigger-width, 56px) !default;
$-fab-actions-padding: var(--wot-actions-padding, 12px) !default;
$-fab-icon-fs: var(--wot-fab-icon-fs, 20px) !default;

/* count-down */
$-count-down-text-color: var(--wot-count-down-text-color, $-color-gray-8) !default;
$-count-down-font-size: var(--wot-count-down-font-size, $-fs-content) !default;
$-count-down-line-height: var(--wot-count-down-line-height, 20px) !default;

/* keyboard */
$-keyboard-key-height: var(--wot-keyboard-key-height, 48px) !default;
$-keyboard-key-font-size: var(--wot-keyboard-key-font-size, 28px) !default;
$-keyboard-key-background: var(--wot-keyboard-key-background, $-color-white) !default;
$-keyboard-key-border-radius: var(--wot-keyboard-key-border-radius, 8px) !default;
$-keyboard-delete-font-size: var(--wot-keyboard-delete-font-size, 16px) !default;
$-keyboard-key-active-color: var(--wot-keyboard-key-active-color, $-color-gray-3) !default;
$-keyboard-button-text-color: var(--wot-keyboard-button-text-color, $-color-white) !default;
$-keyboard-button-background: var(--wot-keyboard--button-background, $-color-theme) !default;
$-keyboard-button-active-opacity: var(--wot-keyboard-button-active-opacity, 0.6) !default;
$-keyboard-background: var(--wot-keyboard-background, $-color-gray-2) !default;
$-keyboard-title-height: var(--wot-keyboard-title-height, 34px) !default;
$-keyboard-title-color: var(--wot-keyboard-title-color, $-color-gray-7) !default;
$-keyboard-title-font-size: var(--wot-keyboard-title-font-size, 16px) !default;
$-keyboard-close-padding: var(--wot-keyboard-title-font-size, 0 16px) !default;
$-keyboard-close-color: var(--wot-keyboard-close-color, $-color-theme) !default;
$-keyboard-close-font-size: var(--wot-keyboard-close-font-size, 14px) !default;
$-keyboard-icon-size: var(--wot-keyboard-icon-size, 22px) !default;

/* number-keyboard */
$-number-keyboard-key-height: var(--wot-number-keyboard-key-height, 48px) !default;
$-number-keyboard-key-font-size: var(--wot-number-keyboard-key-font-size, 28px) !default;
$-number-keyboard-key-background: var(--wot-number-keyboard-key-background, $-color-white) !default;
$-number-keyboard-key-border-radius: var(--wot-number-keyboard-key-border-radius, 8px) !default;
$-number-keyboard-delete-font-size: var(--wot-number-keyboard-delete-font-size, 16px) !default;
$-number-keyboard-key-active-color: var(--wot-number-keyboard-key-active-color, $-color-gray-3) !default;
$-number-keyboard-button-text-color: var(--wot-number-keyboard-button-text-color, $-color-white) !default;
$-number-keyboard-button-background: var(--wot-number-keyboard--button-background, $-color-theme) !default;
$-number-keyboard-button-active-opacity: var(--wot-number-keyboard-button-active-opacity, 0.6) !default;
$-number-keyboard-background: var(--wot-number-keyboard-background, $-color-gray-2) !default;
$-number-keyboard-title-height: var(--wot-number-keyboard-title-height, 34px) !default;
$-number-keyboard-title-color: var(--wot-number-keyboard-title-color, $-color-gray-7) !default;
$-number-keyboard-title-font-size: var(--wot-number-keyboard-title-font-size, 16px) !default;
$-number-keyboard-close-padding: var(--wot-number-keyboard-title-font-size, 0 16px) !default;
$-number-keyboard-close-color: var(--wot-number-keyboard-close-color, $-color-theme) !default;
$-number-keyboard-close-font-size: var(--wot-number-keyboard-close-font-size, 14px) !default;
$-number-keyboard-icon-size: var(--wot-number-keyboard-icon-size, 22px) !default;

/* passwod-input */
$-password-input-height: var(--wot-password-input-height, 50px);
$-password-input-margin: var(--wot-password-input-margin, 16px);
$-password-input-font-size: var(--wot-password-input-margin, 20px);
$-password-input-radius: var(--wot-password-input-radius, 6px);
$-password-input-background: var(--wot-password-input-background, #fff);
$-password-input-info-color: var(--wot-password-input-info-color, $-color-info);
$-password-input-info-font-size: var(--wot-password-input-info-font-size, $-fs-content);
$-password-input-border-color: var(--wot-password-border-color, #ebedf0);
$-password-input-error-info-color: var(--wot-password-input-error-info-color, $-color-danger);
$-password-input-dot-size: var(--wot-password-input-dot-size, 10px);
$-password-input-dot-color: var(--wot-password-input-dot-color, $-color-gray-8);
$-password-input-text-color: var(--wot-password-input-text-color, $-color-gray-8);
$-password-input-cursor-color: var(--wot-password-input-cursor-color, $-color-gray-8);
$-password-input-cursor-width: var(--wot-password-input-cursor-width, 1px);
$-password-input-cursor-height: var(--wot-password-input-cursor-height, 40%);
$-password-input-cursor-duration: var(--wot-password-input-cursor-duration, 1s);

/* form-item */
$-form-item-error-message-color: var(--wot-form-item-error-message-color, $-color-danger) !default;
$-form-item-error-message-font-size: var(--wot-form-item-error-message-font-size, $-fs-secondary) !default;
$-form-item-error-message-line-height: var(--wot-form-item-error-message-line-height, 24px) !default;

/* backtop */
$-backtop-bg: var(--wot-backtop-bg, #e1e1e1) !default;
$-backtop-icon-size: var(--wot-backtop-icon-size, 20px) !default;

/* index-bar */
$-index-bar-index-font-size: var(--wot-index-bar-index-font-size, $-fs-aid) !default;

/* text */
$-text-info-color: var(--wot-text-info-color, $-color-info) !default;
$-text-primary-color: var(--wot-text-primary-color, $-color-theme) !default;
$-text-error-color: var(--wot-text-error-color, $-color-danger) !default;
$-text-warning-color: var(--wot-text-warning-color, $-color-warning) !default;
$-text-success-color: var(--wot-text-success-color, $-color-success) !default;

/* video-preview */
$-video-preview-bg: var(--wot-video-preview-bg, rgba(0, 0, 0, 0.8)) !default; // 背景色
$-video-preview-close-color: var(--wot-video-preview-close-color, #fff) !default; // 图标颜色
$-video-preview-close-font-size: var(--wot-video-preview-close-font-size, 20px) !default; // 图标大小

/* img-cropper */
$-img-cropper-icon-size: var(--wot-img-cropper-icon-size, $-fs-big) !default; // 图标大小
$-img-cropper-icon-color: var(--wot-img-cropper-icon-color, #fff) !default; // 图标颜色

/* floating-panel */
$-floating-panel-bg: var(--wot-floating-panel-bg, $-color-white) !default; // 背景色
$-floating-panel-radius: var(--wot-floating-panel-radius, 16px) !default; // 圆角
$-floating-panel-z-index: var(--wot-floating-panel-z-index, 99) !default; // 层级
$-floating-panel-header-height: var(--wot-floating-panel-header-height, 30px) !default; // 头部高度
$-floating-panel-bar-width: var(--wot-floating-panel-bar-width, 20px) !default; // bar 宽度
$-floating-panel-bar-height: var(--wot-floating-panel-bar-height, 3px) !default; // bar 高度
$-floating-panel-bar-bg: var(--wot-floating-panel-bar-bg, $-color-gray-5) !default; // bar 背景色
$-floating-panel-bar-radius: var(--wot-floating-panel-bar-radius, 4px) !default; // bar 圆角
$-floating-panel-content-bg: var(--wot-floating-panel-content-bg, $-color-white) !default; // 内容背景色

/* signature */
$-signature-bg: var(--wot-signature-bg, $-color-white) !default; // 背景色
$-signature-radius: var(--wot-signature-radius, 4px) !default; // 圆角
$-signature-border: var(--wot-signature-border, 1px solid $-color-gray-5) !default; // 边框圆角
$-signature-footer-margin-top: var(--wot-signature-footer-margin-top, 8px) !default; // 底部按钮上边距
$-signature-button-margin-left: var(--wot-signature-button-margin-left, 8px) !default; // 底部按钮左边距



