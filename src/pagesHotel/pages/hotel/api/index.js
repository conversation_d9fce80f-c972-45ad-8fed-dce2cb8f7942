import { request } from '/src/utils/request/index.js'
// 酒店列表
export async function getHotelList(payload = {}) {
  return await request('/applet/tourist/hotel/getPage', { data: payload })
}
// 酒店详情
export async function getHotelDetail(payload = {}) {
  return await request('/applet/tourist/hotel/getDetail', { data: payload })
}
// 房型list
export async function getRoomList(payload = {}) {
  return await request('/applet/tourist/room/roomType/getList', { data: payload })
}
// 房型详情
export async function getRoomDetail(payload = {}) {
  return await request('/applet/tourist/room/roomType/getDetail', { data: payload })
}

// 获取可用优惠券
export async function getOrderCoupon(payload = {}) {
  return await request('/applet/coupon/myCoupon/getOrderCoupon', { data: payload })
}

// 创建订单
export async function setHotelOrder(payload = {}) {
  return await request('/applet/orderManage/order/setHotelOrder', { data: payload })
}

// 获取客户列表
export async function getClientList(payload = {}) {
  return await request('/applet/clientManage/getList', { data: payload })
}

// 添加客户
export async function setAddClient(payload = {}) {
  return await request('/applet/clientManage/setAdd', { data: payload })
}

// 更新客户
export async function setUpdateClient(payload = {}) {
  return await request('/applet/clientManage/setUpdate', { data: payload })
}

// 删除客户
export async function setDeleteClient(payload = {}) {
  return await request('/applet/clientManage/setDelete', { data: payload })
}

export async function getAvaliableRoomNums(payload = {}) {
  return await request('/applet/tourist/room/roomType/getAvaliableroomnums', {
    data: payload,
  })
}
