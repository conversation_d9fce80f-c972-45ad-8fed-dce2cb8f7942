<template>
  <div>
    <wd-form ref="form" :model="formData" errorType="toast">
      <wd-cell-group :border="true" custom-class="m-4 bg-white rounded-lg overflow-hidden">
        <wd-input
          required
          label="住客姓名"
          label-width="100px"
          prop="nickname"
          align-right
          placeholder="请输入住客姓名"
          v-model="formData.nickname"
          :rules="[{ required: true, message: '请输入住客姓名' }]"
        />

        <wd-input
          label="联系方式"
          label-width="100px"
          prop="telephone"
          v-model="formData.telephone"
          placeholder="请输入联系方式"
          align-right
          :rules="[
            { required: true, message: '请输入联系方式' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系电话' },
          ]"
        />
        <wd-input
          label="身份证号"
          label-width="100px"
          prop="identity_card"
          v-model="formData.identity_card"
          placeholder="请输入身份证号"
          align-right
        />
      </wd-cell-group>
    </wd-form>
    <view class="px-4 pt-8 flex flex-col gap-4">
      <wd-button custom-style="border-radius:4px;width:100%" type="primary" @click="addClient">
        保存
      </wd-button>
      <wd-button
        plain
        custom-style="border-radius:4px;width:100%"
        type="primary"
        @click="deleteClient"
        v-if="id"
      >
        删除
      </wd-button>
    </view>
    <wd-message-box />
  </div>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref, getCurrentInstance } from 'vue'
import { setAddClient, setUpdateClient, setDeleteClient } from '../api'
import { useMessage } from '@/uni_modules/wot-design-uni'
const message = useMessage()
const { proxy } = getCurrentInstance()

const id = ref('')
const formData = ref({
  nickname: '',
  telephone: '',
  identity_card: '',
})
onLoad((e) => {
  id.value = e.id
  formData.value = e.data ? JSON.parse(e.data) : {}
})

const form = ref(null)
function addClient() {
  form.value.validate().then(({ valid }) => {
    if (!valid) return
    if (id.value) {
      setUpdateClient(formData.value).then((res) => {
        uni.showToast({
          title: '保存成功',
          icon: 'success',
        })
        back()
      })
    } else {
      setAddClient({ id: id.value, ...formData.value }).then((res) => {
        uni.showToast({
          title: '保存成功',
          icon: 'success',
        })
        back()
      })
    }
  })
}

function deleteClient() {
  message
    .confirm({
      msg: '是否删除该客户信息',
      title: '提示',
    })
    .then(() => {
      setDeleteClient({
        id: id.value,
      }).then((res) => {
        uni.showToast({
          title: '删除成功',
          icon: 'success',
        })
        back()
      })
    })
}

function back() {
  setTimeout(() => {
    uni.navigateBack()
    console.log(getCurrentInstance())
    proxy.getOpenerEventChannel().emit('reload')
  }, 200)
}
</script>

<style lang="scss" scoped></style>
