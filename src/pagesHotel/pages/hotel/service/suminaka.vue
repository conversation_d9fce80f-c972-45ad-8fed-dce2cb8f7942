<template>
  <view class="pt-safe bg-gray-50 min-h-screen pb-24">
    <!-- 服务类型 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <view class="flex items-center justify-between py-3">
        <text class="text-sm text-gray-800">
          <text class="text-red-500">*</text>
          服务类型
        </text>
        <view class="flex-1 ml-4">
          <wd-select-picker
            v-model="formData.serviceType"
            :columns="serviceTypeOptions"
            placeholder="请选择"
            custom-style="text-align: right; font-size: 14px;"
            :border="false"
          />
        </view>
      </view>
    </view>

    <!-- 房间编号 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <view class="flex items-center justify-between py-3">
        <text class="text-sm text-gray-800">
          <text class="text-red-500">*</text>
          房间编号
        </text>
        <view class="flex-1 ml-4">
          <wd-input
            v-model="formData.roomNumber"
            placeholder="请输入"
            :border="false"
            custom-style="text-align: right; font-size: 14px;"
          />
        </view>
      </view>
    </view>

    <!-- 联系电话 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <view class="flex items-center justify-between py-3">
        <text class="text-sm text-gray-800">
          <text class="text-red-500">*</text>
          联系电话
        </text>
        <view class="flex-1 ml-4">
          <wd-input
            v-model="formData.phone"
            type="number"
            placeholder="请输入"
            :border="false"
            custom-style="text-align: right; font-size: 14px;"
          />
        </view>
      </view>
    </view>

    <!-- 服务事项 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-sm text-gray-800 mb-3 block">服务事项</text>
      <wd-input
        v-model="formData.serviceDescription"
        type="textarea"
        placeholder="请输入您的需求"
        :border="false"
        :maxlength="300"
        :show-word-limit="true"
        custom-style="min-height: 100px; font-size: 14px;"
        @input="handleDescriptionInput"
      />
      <view class="flex justify-end mt-2">
        <text class="text-xs text-gray-400">{{ descriptionLength }}/300</text>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3 pb-safe">
      <wd-button
        type="primary"
        size="large"
        custom-style="width: 100%; border-radius: 8px;"
        @click="handleSubmit"
        :disabled="!isFormValid"
      >
        提交
      </wd-button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 表单数据
const formData = ref({
  serviceType: '',
  roomNumber: '',
  phone: '',
  serviceDescription: '',
})

// 服务类型选项
const serviceTypeOptions = ref([
  {
    value: '房间清洁',
    label: '房间清洁',
  },
  {
    value: '维修服务',
    label: '维修服务',
  },
  {
    value: '客房用品',
    label: '客房用品',
  },
  {
    value: '餐饮服务',
    label: '餐饮服务',
  },
  {
    value: '其他服务',
    label: '其他服务',
  },
])

// 服务描述字数统计
const descriptionLength = computed(() => {
  return formData.value.serviceDescription ? formData.value.serviceDescription.length : 0
})

// 表单验证
const isFormValid = computed(() => {
  return formData.value.serviceType && formData.value.roomNumber && formData.value.phone
})

// 处理服务描述输入
const handleDescriptionInput = (e) => {
  formData.value.serviceDescription = e.detail.value
}

// 处理提交
const handleSubmit = () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none',
    })
    return
  }

  // 简单的手机号验证
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(formData.value.phone)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
    })
    return
  }

  // 提交成功提示
  uni.showModal({
    title: '提交成功',
    content: '稍后有人员给你服务',
    showCancel: false,
    confirmText: '确定',
    success: () => {
      // 重置表单
      formData.value = {
        serviceType: '',
        roomNumber: '',
        phone: '',
        serviceDescription: '',
      }
    },
  })
}

// 页面配置
onLoad(() => {
  uni.setNavigationBarTitle({
    title: '住中服务',
  })
})
</script>

<style scoped>
/* 自定义选择器样式
::v-deep .wd-select-picker {
  .wd-select-picker__value {
    color: #999;
    font-size: 14px;
  }

  .wd-select-picker__value--placeholder {
    color: #999;
  }
}

/* 自定义输入框样式 
::v-deep .wd-input {
  .wd-input__inner {
    color: #333;
    font-size: 14px;
  }

  .wd-input__placeholder {
    color: #999;
    font-size: 14px;
  }
} */
</style>
