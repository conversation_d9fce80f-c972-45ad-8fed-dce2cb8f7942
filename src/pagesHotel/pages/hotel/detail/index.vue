<template>
  <view class="hotel-detail pb-safe">
    <!-- 轮播图背景 -->
    <view class="absolute top-0 left-0 right-0 z-0">
      <wd-swiper
        :list="hotelInfo.imge ? hotelInfo.imge.split(',') : []"
        :autoplay="true"
        :duration="300"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#fff"
        height="230"
      />
    </view>

    <!-- 内容区域 -->
    <view class="relative z-10 pt-48">
      <!-- 悬浮的酒店信息卡片 -->
      <view class="bg-white mx-4 mb-4 rounded-lg p-4">
        <view class="flex items-start justify-between mb-3">
          <view class="flex-1">
            <text class="text-lg font-medium text-gray-800 mb-2 block">{{ hotelInfo.name }}</text>
            <view class="flex items-center gap-2 mb-2">
              <wd-tag
                :color="'#244190'"
                :bg-color="'#d3d7e6'"
                v-for="tag in hotelInfo.service_facility"
                :key="tag"
              >
                {{ tag }}
              </wd-tag>
            </view>
          </view>
          <wd-img
            :width="18"
            :height="18"
            src="/pagesHotel/static/navigation.png"
            mode="aspectFit"
            @click="handleNavigation"
          />
        </view>

        <!-- 地址 -->
        <view class="flex items-center mb-3">
          <wd-img
            :width="12"
            :height="12"
            src="/pagesHotel/static/positioning.png"
            mode="aspectFit"
          />
          <text class="text-sm text-gray-600 flex-1 ml-2">{{ hotelInfo.address }}</text>
        </view>
      </view>

      <!-- 会员信息 -->
      <!-- <view class="bg-white mx-4 mt-4 rounded-lg p-4">
        <view class="flex items-center">
          <wd-img src="/static/demo/vip.png" width="48" height="48" class="mr-2" />
          <view>
            <view class="text-[#333333] text-[30rpx] mr-4">
              黑铁会员
              <text class="text-[#999999] text-[24rpx]">（您当前的会员等级）</text>
            </view>
            <view class="text-[24rpx] mt-2">
              享有订房折扣
              <text class="text-[#243781] text-[30rpx]">8.8</text>
              折
            </view>
          </view>
        </view>
      </view> -->

      <!-- 日期选择区域 -->
      <view class="bg-white mx-4 mt-4 rounded-lg p-4">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="text-base text-gray-800">
              {{ dateCalendarValue.start }} ~ {{ dateCalendarValue.end }}
            </text>
            <text class="text-sm text-gray-500 ml-4">共{{ dateCalendarValue.interval }}晚</text>
          </view>
          <wd-icon name="arrow-right" size="16px" color="#999" @click="handleDateSelect" />
        </view>
      </view>

      <!-- 房型选择标签 -->
      <view class="mx-4 mt-4">
        <scroll-view scroll-x="true" class="whitespace-nowrap">
          <view class="flex space-x-3 pb-2">
            <view
              :class="[
                'inline-block px-4 py-2 rounded-full text-sm border whitespace-nowrap',
                selectedRoomType === 0
                  ? 'bg-[#244190] text-white border-[#244190]'
                  : 'bg-white text-gray-600 border-gray-200',
              ]"
              @click="selectedRoomType = 0"
            >
              全部房型
            </view>
            <view
              v-for="(roomType, index) in roomList"
              :key="index"
              :class="[
                'inline-block px-4 py-2 rounded-full text-sm border whitespace-nowrap',
                selectedRoomType === roomType.id
                  ? 'bg-[#244190] text-white border-[#244190]'
                  : 'bg-white text-gray-600 border-gray-200',
              ]"
              @click="selectedRoomType = roomType.id"
            >
              {{ roomType.name }}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 房间列表 -->
      <view class="mx-4 mt-4 space-y-4">
        <view
          v-for="room in filteredRooms"
          :key="room.id"
          class="bg-white rounded-lg p-4 shadow-sm"
          @click="handleBookRoom(room)"
        >
          <view class="flex">
            <!-- 房间图片 -->
            <view class="flex-shrink-0 mr-4">
              <wd-img
                :src="room.imge ? room.imge.split(',')[0] : ''"
                :width="100"
                :height="80"
                radius="8rpx"
                mode="aspectFill"
              />
              <text class="text-xs text-gray-500 text-center block mt-1">
                剩余{{ room.avaliable_rooms }}间
              </text>
            </view>

            <!-- 房间信息 -->
            <view class="flex-1">
              <text class="text-base font-medium text-gray-800 mb-2 block">{{ room.name }}</text>

              <!-- 设施标签 -->
              <view class="flex flex-wrap gap-2 mb-3">
                <wd-tag
                  v-for="facility in room.facilities"
                  :key="facility"
                  bg-color="#f0f0f0"
                  color="#666"
                  size="small"
                >
                  {{ facility }}
                </wd-tag>
              </view>

              <!-- 智能门锁 -->
              <view class="flex items-center mb-3">
                <wd-icon name="check-circle" size="14px" color="#4CAF50" class="mr-1" />
                <text class="text-xs text-green-600">支持智能门锁</text>
              </view>

              <!-- 价格和预订 -->
              <view class="flex items-end justify-between">
                <view class="flex items-end text-red-500">
                  <text class="text-sm mr-1">¥</text>
                  <text class="text-xl font-bold">{{ room.price }}.00</text>
                </view>
                <wd-button type="primary" size="small">订</wd-button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部选项卡 -->
      <view class="mt-8 mx-4 mb-20">
        <wd-tabs custom-class="w-full m-0!" v-model="activeTab" @change="handleTabChange">
          <wd-tab title="酒店概况" name="overview">
            <view class="p-4 text-sm text-gray-600">
              <rich-text :nodes="hotelInfo.information" />
            </view>
          </wd-tab>
          <wd-tab title="规定政策" name="policy">
            <view class="p-4 text-sm text-gray-600">
              <rich-text :nodes="hotelInfo.policy" />
            </view>
          </wd-tab>
        </wd-tabs>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getHotelDetail, getRoomList } from '../api'
import { useHotel } from '/src/utils/hook/useHotel'
const { dateCalendarValue } = useHotel()

// 页面数据
const hotelInfo = ref({})
// 房型选择
const selectedRoomType = ref(0)
// 房间列表数据
const roomList = ref([])

// 底部选项卡
const activeTab = ref('overview')

// 过滤房间列表
const filteredRooms = computed(() => {
  if (selectedRoomType.value === 0) {
    return roomList.value
  }
  return roomList.value.filter((room) => room.id === selectedRoomType.value)
})

// 处理分享
const handleShare = () => {
  console.log('分享酒店')
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none',
  })
}

// 处理日期选择
const handleDateSelect = () => {
  console.log('选择日期')
  uni.showToast({
    title: '日期选择功能开发中',
    icon: 'none',
  })
}

// 处理房间预订
const handleBookRoom = (room) => {
  uni.navigateTo({
    url: `/pagesHotel/pages/hotel/room/detail?id=${room.id}`,
  })
}

// 处理选项卡切换
const handleTabChange = (name) => {
  console.log('切换选项卡:', name)
}

const handleNavigation = () => {
  uni.openLocation({
    latitude: Number(hotelInfo.value.lat),
    longitude: Number(hotelInfo.value.lon),
    scale: 12,
    name: hotelInfo.value.name,
    address: hotelInfo.value.address,
  })
}

// 接收页面参数
onLoad((options) => {
  if (options.id) {
    getHotelDetailData(options.id)
    getRoomListData(options.id)
  }
})

function getRoomListData(id) {
  getRoomList({
    hotel_id: id,
    start_date: dateCalendarValue.value.start,
    end_date: dateCalendarValue.value.end,
  }).then((res) => {
    roomList.value = res.data
  })
}

function getHotelDetailData(id) {
  getHotelDetail({ id }).then((res) => {
    hotelInfo.value = res.data
  })
}
</script>

<style scoped lang="scss">
// 页面容器
.hotel-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 轮播图样式
::v-deep .wd-swiper {
  .wd-swiper-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

// 房型标签滚动样式
::-webkit-scrollbar {
  display: none;
}

// 卡片悬浮效果
.shadow-sm:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

// 标签样式增强
::v-deep .wd-tag {
  font-size: 12px;
}

// 选项卡样式
::v-deep .wd-tabs {
  background-color: white;
  border-radius: 8px;
  margin: 0 16px;
  overflow: hidden;
}
</style>
