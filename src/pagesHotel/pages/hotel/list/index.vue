<template>
  <z-paging ref="paging" v-model="hotelList" @query="queryList" empty-view-text="暂无酒店数据">
    <!-- 酒店卡片列表 -->
    <view
      v-for="(hotel, index) in hotelList"
      :key="hotel.id"
      class="mx-4 mb-3 bg-white rounded-lg overflow-hidden shadow-sm"
      @click="goToHotelDetail(hotel)"
    >
      <view class="flex p-3 h-170rpx">
        <!-- 酒店图片 -->
        <view class="flex-shrink-0 mr-3">
          <wd-img
            :src="hotel.imge && hotel.imge.split(',')[0]"
            radius="8rpx"
            :width="96"
            :height="85"
          />
        </view>

        <!-- 酒店信息 -->
        <view class="flex-1 flex flex-col justify-between">
          <!-- 酒店名称 -->
          <text class="text-32rpx text-[#333333] leading-tight mb-2">
            {{ hotel.name }}
          </text>

          <!-- 标签区域 -->
          <view class="flex items-center mb-2 gap-2">
            <wd-tag
              :color="'#244190'"
              :bg-color="'#d3d7e6'"
              v-for="(tag, tagIndex) in hotel.service_facility"
            >
              {{ tag }}
            </wd-tag>
          </view>

          <!-- 价格 -->
          <view class="flex items-end text-[#FF3535] h-50rpx">
            <text class="text-24rpx pb-2rpx mr-2rpx">¥</text>
            <text class="text-36rpx">{{ hotel.starting_price }}</text>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getHotelList } from '../api'
// 分页组件引用
const paging = ref(null)

const hotelList = ref([])

// 查询列表数据
const queryList = async (pageNo, pageSize) => {
  try {
    const {
      data: { records, total },
    } = await getHotelList({
      current: pageNo,
      size: pageSize,
    })
    paging.value.complete(records)
  } catch (error) {
    paging.value.complete(false)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
}

// 跳转到酒店详情
const goToHotelDetail = (hotel) => {
  uni.navigateTo({
    url: `/pagesHotel/pages/hotel/detail/index?id=${hotel.id}`,
  })
}

// 页面生命周期
onMounted(() => {
  console.log('酒店列表页面加载完成')
})
</script>

<style scoped lang="scss">
// 卡片悬浮效果
.shadow-sm:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

// 图片加载占位
image {
  background-color: #f5f5f5;
}

// z-paging自定义样式
::v-deep .z-paging-content {
  padding-top: 12px;
}

// 空数据提示样式优化
::v-deep .z-paging-empty-view {
  padding: 60px 20px;
}
</style>
