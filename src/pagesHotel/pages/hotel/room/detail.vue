<template>
  <view class="pb-safe bg-gray-50 min-h-screen">
    <!-- 顶部轮播图 -->
    <view class="relative" v-if="roomInfo.imge">
      <wd-swiper
        :list="roomInfo.imge ? roomInfo.imge.split(',') : []"
        autoplay
        height="280"
        v-model:current="currentImageIndex"
        :indicator="{ type: 'fraction' }"
        indicatorPosition="bottom-right"
        @change="handleSwiperChange"
      />
    </view>

    <!-- 房型名称卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <view class="text-36rpx font-500 text-#333333 pb-4">{{ roomInfo?.name }}</view>
      <view class="grid grid-cols-4 py-2 gap-1 bg-#f7f7f7 rounded-8rpx">
        <view class="flex flex-col items-center text-center">
          <wd-img
            src="/pagesHotel/static/zhengtao.png"
            :width="32"
            :height="32"
            mode="aspectFit"
            class="mb-2"
          />
          <text class="text-[24rpx] text-gray-400 mb-1 font-500">超大空间</text>
          <text class="text-[20rpx] text-gray-400">
            房间{{ roomInfo?.room_size?.width || 20 }}-{{ roomInfo?.room_size?.height || 20 }}平米
          </text>
        </view>
        <view class="flex flex-col items-center text-center">
          <wd-img
            src="/pagesHotel/static/woshi.png"
            :width="32"
            :height="32"
            mode="aspectFit"
            class="mb-2"
          />
          <text class="text-[24rpx] text-gray-400 mb-1 font-500">智能门锁</text>
          <text class="text-[20rpx] text-gray-400">可手机开门</text>
        </view>
        <view class="flex flex-col items-center text-center">
          <wd-img
            src="/pagesHotel/static/bed.png"
            :width="32"
            :height="32"
            mode="aspectFit"
            class="mb-2"
          />
          <text class="text-[24rpx] text-gray-400 mb-1 font-500">
            宜住{{ roomInfo?.people_sum || 1 }}人
          </text>
          <text class="text-[20rpx] text-gray-400">
            大床{{ roomInfo?.bed_size?.width }}*{{ roomInfo?.bed_size?.height }}米*{{
              roomInfo?.bed_sum || 1
            }}张
          </text>
        </view>
        <view class="flex flex-col items-center text-center">
          <wd-img
            src="/pagesHotel/static/kehu.png"
            :width="32"
            :height="32"
            mode="aspectFit"
            class="mb-2"
          />
          <text class="text-[24rpx] text-gray-400 mb-1 font-500">专业客服</text>
          <text class="text-[20rpx] text-gray-400">24小时在线服务</text>
        </view>
      </view>
    </view>

    <!-- 配套设施卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-base font-medium text-gray-800 mb-4 block">配套设施</text>
      <view class="grid gap-2" :class="roomFacilities.length > 5 ? 'grid-cols-5' : 'grid-cols-4'">
        <view
          v-for="facility in roomFacilities"
          :key="facility.id"
          class="flex flex-col items-center text-center"
        >
          <view class="bg-#f7f7f7 rounded-full w-12 h-12 flex items-center justify-center">
            <wd-img :src="facility.url" :width="24" :height="24" mode="aspectFit" />
          </view>
          <text class="text-[20rpx] text-gray-400 pt-2">{{ facility.name }}</text>
        </view>
      </view>
    </view>

    <!-- 入住须知卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-base font-medium text-gray-800 mb-4 block">入住须知</text>
      <view class="space-y-3" v-if="roomInfo?.hour_room === '2'">
        <view class="flex items-center">
          <text class="text-sm text-gray-600 w-20">入住时间:</text>
          <text class="text-sm text-gray-800">{{ roomInfo?.enter_time }}</text>
        </view>
        <view class="flex items-center">
          <text class="text-sm text-gray-600 w-20">退房时间:</text>
          <text class="text-sm text-gray-800">{{ roomInfo?.quit_time }}</text>
        </view>
      </view>
      <view class="space-y-3" v-else>
        <view class="flex items-center">
          <text class="text-sm text-gray-600 w-20">入住时长:</text>
          <text class="text-sm text-gray-800">{{ roomInfo?.enter_duration }}</text>
        </view>
      </view>
    </view>

    <!-- 房型详情卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4 mb-20">
      <text class="text-base font-medium text-gray-800 mb-4 block">房型详情</text>
      <view class="text-sm text-gray-600 leading-relaxed">
        <rich-text :nodes="roomInfo?.information" />
      </view>
    </view>

    <!-- 底部固定预定栏 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 pb-safe">
      <!-- 咨询按钮 -->
      <view class="flex items-center justify-between">
        <wd-button custom-class="mx-2!" size="medium" @click="handleConsult" type="text">
          <wd-icon name="chat" size="16px" class="mr-1" />
          咨询
        </wd-button>

        <!-- 价格 -->
        <view class="flex-1 text-center">
          <text class="text-red-500 text-lg font-bold">¥{{ roomInfo?.price }}</text>
        </view>
        <!-- 立即预定按钮 -->
        <wd-button
          type="primary"
          @click="handleBookNow"
          size="large"
          custom-style="width: 60%;height-100%;--wot-button-large-radius:0px;"
          :round="false"
        >
          立即预定
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getRoomDetail } from '../api'
import { useHotel } from '/src/utils/hook/useHotel'
const { dateCalendarValue } = useHotel()

// 当前轮播图索引
const currentImageIndex = ref(0)

// 房间信息
const roomInfo = ref({})

// 配套设施
const roomFacilities = computed(() => {
  const allFacilities = [
    { id: '1', url: '/pagesHotel/static/wifi.png', name: 'WIFI' },
    { id: '2', url: '/pagesHotel/static/tv.png', name: '投影' },
    { id: '3', url: '/pagesHotel/static/kongtiao.png', name: '空调' },
    { id: '4', url: '/pagesHotel/static/bingxiang.png', name: '冰箱' },
    { id: '5', url: '/pagesHotel/static/fangyuan.png', name: '多功能淋浴房' },
  ]
  return (
    allFacilities.filter((facility) => roomInfo.value.service_facility?.includes(facility.id)) || []
  )
})

// 轮播图切换
const handleSwiperChange = (index) => {
  currentImageIndex.value = index
}

// 咨询功能
const handleConsult = () => {
  console.log('咨询客服')
  uni.showToast({
    title: '客服咨询功能开发中',
    icon: 'none',
  })
}

// 立即预定
const handleBookNow = () => {
  console.log('立即预定房间', roomInfo.value)
  uni.navigateTo({
    url: `/pagesHotel/pages/hotel/room/reservation?room_type=${roomInfo.value.room_type}&hotel_id=${roomInfo.value.hotel_id}&price=${roomInfo.value.price}&room_name=${roomInfo.value.name}`,
  })
}

// 接收页面参数
onLoad((options) => {
  getRoomDetailInfo(options.id)
})

const getRoomDetailInfo = async (id) => {
  const res = await getRoomDetail({
    id,
    start_date: dateCalendarValue.value.start,
    end_date: dateCalendarValue.value.end,
  })
  roomInfo.value = res.data
}
</script>
