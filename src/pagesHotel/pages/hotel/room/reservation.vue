<template>
  <view class="pb-safe bg-gray-50 min-h-screen">
    <!-- 预定房型信息卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-base font-medium text-gray-800 mb-3 block">预定房型信息</text>
      <view class="flex items-center justify-between mb-3">
        <text class="text-sm text-gray-600">
          {{ formData.start_date ? dayjs(formData.start_date).format('MM月DD日') : '' }} ~
          {{ formData.end_date ? dayjs(formData.end_date).format('MM月DD日') : '' }}
        </text>
        <text class="text-sm text-gray-600">共{{ formData.interval }}晚</text>
      </view>
      <text class="text-base text-gray-800">{{ formData.room_name }}</text>
    </view>

    <!-- 入住信息卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-base font-medium text-gray-800 mb-4 block">入住信息</text>

      <!-- 房间数量 -->
      <view class="flex items-center justify-between py-3 border-b border-gray-100">
        <text class="text-sm text-gray-800">
          <text class="text-#244190">*</text>
          房间数量
        </text>
        <view class="flex items-center" @click="handleRoomCountSelect">
          <text class="text-sm text-gray-800 mr-2">{{ formData.guest_nums }}间</text>
          <wd-icon name="arrow-right" size="16px" color="#999" />
        </view>
      </view>

      <!-- 房间1住客 -->
      <view v-for="i in formData.guest_nums" :key="i" class="flex items-center py-3">
        <view class="text-sm text-gray-800 w-160rpx">
          <text class="text-#244190">*</text>
          房间{{ i }}
        </view>
        <view class="flex flex-1" @click="handleGuestSelect(i)">
          <view class="text-sm text-left text-gray-400 ml-2">
            {{ formData.room_list[i - 1]?.nickname || '请选择住客' }}
          </view>
        </view>
        <view class="flex items-center">
          <wd-icon name="user-add" size="14px" color="#244190" />
          <wd-img src="/pagesHotel/static/households.png" :width="'16px'" :height="'16px'" />
        </view>
      </view>
    </view>

    <!-- 在线选房卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-base font-medium text-gray-800 mb-4 block">在线选房</text>

      <view class="flex items-center justify-between py-3 border-b border-gray-100">
        <text class="text-sm text-gray-800">房间编号</text>
        <view class="flex items-center" @click="handleRoomNumberSelect">
          <wd-icon name="arrow-right" size="16px" color="#999" />
        </view>
      </view>

      <view class="flex items-center mt-3 bg-#eaecf3 rounded-4rpx p-1 gap-2">
        <wd-icon name="info-circle-filled" :color="'#244190'"></wd-icon>
        <text class="text-xs text-[#244190] leading-relaxed">
          仅支持当天入住时，才能在线上自行选择房间号
        </text>
      </view>
    </view>

    <!-- 优惠折扣卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <text class="text-base font-medium text-gray-800 mb-4 block">优惠折扣</text>

      <!-- 会员优惠 -->
      <!-- <view class="flex items-center justify-between py-3 border-b border-gray-100">
        <view class="flex items-center">
          <text class="text-sm text-gray-800 mr-2">会员优惠</text>
          <text class="text-xs text-red-500 bg-red-50 px-2 py-1 rounded">订房8.8折</text>
        </view>
        <text class="text-sm text-red-500">-¥{{ memberDiscount }}.00</text>
      </view> -->

      <!-- 优惠券 -->
      <view class="flex items-center justify-between py-3" @click="handleCouponSelect">
        <text class="text-sm text-gray-800">优惠券</text>
        <view class="flex items-center">
          <text v-if="couponDiscount > 0" class="text-sm text-red-500 mr-2">
            -¥{{ couponDiscount }}
          </text>
          <text v-else class="text-sm text-gray-400 mr-2">请选择优惠券</text>
          <wd-icon name="arrow-right" size="16px" color="#999" />
        </view>
      </view>
    </view>

    <!-- 其他说明卡片 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4 mb-20">
      <text class="text-base font-medium text-gray-800 mb-4 block">其他说明</text>

      <view class="space-y-3">
        <view>
          <text class="text-sm text-gray-600">发票：</text>
          <text class="text-sm text-gray-800">请联系客服开具发票</text>
        </view>

        <view>
          <text class="text-sm text-gray-600">退款说明：</text>
          <text class="text-sm text-gray-800">取消订单，将收取10%房费作为违约金支付给酒店</text>
        </view>
      </view>
    </view>

    <popup-detail ref="popupDetailRef" @change="handleCouponChange"></popup-detail>
    <popup-user ref="popupUserRef" @change="handleGuestSelectChange"></popup-user>
    <popup-room ref="popupRoomRef" @change="handleRoomSelectChange"></popup-room>

    <!-- 底部提交栏 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 pb-safe">
      <view class="flex items-center justify-between">
        <!-- 价格信息 -->
        <view class="flex flex-col px-4">
          <view class="flex items-end">
            <text class="text-red-500 text-lg font-bold mr-1">¥{{ finalPrice }}</text>
          </view>
          <text class="text-xs text-gray-400 line-through">¥{{ originalPrice }}</text>
        </view>

        <!-- 提交按钮 -->
        <wd-button
          type="primary"
          size="large"
          @click="handleSubmitOrder"
          custom-style="width: 60%;height-100%;--wot-button-large-radius:0px;"
          :round="false"
          :loading="loading"
        >
          提交订单
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import popupDetail from '@/components/popup-detail.vue'
import popupUser from './components/popup-user.vue'
import popupRoom from './components/popup-room.vue'
import { getOrderCoupon, setHotelOrder } from '../api'
import { dayjs } from '@/uni_modules/wot-design-uni/components/common/dayjs'
import { throttle } from '/src/utils/utils.js'

const formData = ref({
  hotel_id: '',
  start_date: '',
  end_date: '',
  room_type: '',
  room_name: '',
  room_code: '',
  order_type: 0,
  coupon_id: '',
  guest_nums: 1,
  room_list: [],
  interval: 1,
  price: 0,
})

const finalPrice = computed(() => {
  return originalPrice.value - couponDiscount.value
})

const originalPrice = computed(() => {
  return formData.value.price * formData.value.guest_nums * formData.value.interval
})

const handleRoomCountSelect = () => {
  uni.showActionSheet({
    itemList: ['1间', '2间', '3间', '4间', '5间'],
    success: (res) => {
      formData.value.guest_nums = res.tapIndex + 1
      const currentLength = formData.value.room_list.length
      const targetLength = formData.value.guest_nums

      if (targetLength > currentLength) {
        formData.value.room_list.push(
          ...Array.from({ length: targetLength - currentLength }, () => ({})),
        )
      } else if (targetLength < currentLength) {
        formData.value.room_list = formData.value.room_list.slice(0, targetLength)
      }
    },
  })
}
const popupUserRef = ref(null)
const selectedIndex = ref(-1)
const handleGuestSelect = (i) => {
  const data = formData.value.room_list[i - 1]
  popupUserRef.value?.openShow(data ? data.id : null)
  selectedIndex.value = i - 1
}

const handleGuestSelectChange = (e) => {
  formData.value.room_list[selectedIndex.value] = e
}

const popupRoomRef = ref(null)
const handleRoomNumberSelect = () => {
  popupRoomRef.value.openShow({
    room_type: formData.value.room_type,
    hotel_id: formData.value.hotel_id,
    start_date: formData.value.start_date,
    end_date: formData.value.end_date,
  })
}
const handleRoomSelectChange = (e) => {
  console.log(e, 'e')
}

const popupDetailRef = ref(null)
const availableCoupons = ref([])
const handleCouponSelect = async () => {
  await loadAvailableCoupons()
  popupDetailRef.value?.openShow(availableCoupons.value, originalPrice.value)
}

const selectedCoupon = ref({})
const handleCouponChange = (e, data) => {
  selectedCoupon.value = data
}

const couponDiscount = computed(() => {
  if (!selectedCoupon.value.id) return 0

  const price = parseFloat(originalPrice.value)
  const couponType = selectedCoupon.value.type

  switch (couponType) {
    case '1':
      return parseFloat(selectedCoupon.value.money)

    case '2':
      return parseFloat(selectedCoupon.value.money)

    case '3':
      const discount = parseFloat(selectedCoupon.value.discount) // 假设discount是折扣率，如0.8表示8折
      return price * (1 - discount)

    default:
      return 0
  }
})
const loadAvailableCoupons = async () => {
  const res = await getOrderCoupon({ scene: '1', hotel_id: formData.value.hotel_id })
  if (res && res.data) {
    availableCoupons.value = res.data
  }
}

const loading = ref(false)
const handleSubmitOrder = () => {
  if (!formData.value.room_list.some((item) => item.id)) {
    uni.showToast({
      title: '请选择房间',
      icon: 'none',
    })
    return
  }
  throttle(async () => {
    loading.value = true
    const res = await setHotelOrder({
      hotel_id: formData.value.hotel_id,
      start_date: formData.value.start_date,
      end_date: formData.value.end_date,
      interval: formData.value.interval,
      guest_nums: formData.value.guest_nums,
      room_list: formData.value.room_list.map((item) => item.id),
      room_code: formData.value.room_code,
      room_type: formData.value.room_type,
      order_type: formData.value.room_code ? 1 : 0,
      coupon_id: selectedCoupon.value.id,
    })

    const { order_code, appId, nonceStr, paySign, signType, timeStamp, delay_time } = res.data
    uni.requestPayment({
      provider: 'wxpay',
      timeStamp: timeStamp,
      nonceStr: nonceStr,
      package: res.data.package,
      signType: signType,
      paySign: paySign,
      success() {
        setTimeout(() => {
          setOrderVerify({ out_trade_no: order_code })
          openPage(1, '支付成功')
        }, 500)
      },
      fail(e) {
        setTimeout(() => {
          uni.showToast({
            icon: 'none',
            title: '取消支付',
            duration: 2000,
          })
        }, 200)
      },
      complete() {
        loading.value = false
      },
    })
  })
}

const openPage = (state, msg) => {
  if (state == 2) {
    uni.navigateTo({
      url: `/pages/pay-state?state=${state}&msg=${msg}`,
    })
  } else {
    uni.redirectTo({
      url: `/pages/pay-state?state=${state}&msg=${msg}`,
    })
  }
}

import { useHotel } from '/src/utils/hook/useHotel'
const { dateCalendarValue } = useHotel()
onLoad((options) => {
  formData.value.hotel_id = options.hotel_id
  formData.value.start_date = dateCalendarValue.value.start
  formData.value.end_date = dateCalendarValue.value.end
  formData.value.interval = dateCalendarValue.value.interval
  formData.value.room_name = options.room_name
  formData.value.room_type = options.room_type
  formData.value.price = options.price
})
</script>
