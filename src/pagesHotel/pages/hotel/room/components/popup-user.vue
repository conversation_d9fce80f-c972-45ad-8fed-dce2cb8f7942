<template>
  <wd-popup
    v-model="show"
    position="bottom"
    :safe-area-inset-bottom="true"
    custom-style="padding: 30rpx 0rpx;height:60%;min-height:400rpx;border-radius: 32rpx 32rpx 0 0;"
  >
    <view class="popup-body">
      <view class="popup-title">请选择</view>
      <view class="popup-list">
        <scroll-view :scroll-y="true" style="height: 100%">
          <wd-checkbox-group @change="onChange" :modelValue="formData" v-if="lists.length">
            <view class="card-body" v-for="item in lists" :key="item.id">
              <view class="flex items-center justify-between">
                <view class="flex gap-2 text-#333333">
                  <wd-icon name="user" :size="'20px'" />
                  <view class="flex flex-col gap-2">
                    <view class="text-32rpx font-500">
                      {{ item.nickname }}
                      <wd-icon
                        name="edit-outline"
                        :size="'18px'"
                        custom-class="ml-2"
                        :color="'#1f3e7b'"
                        @click="openPage(item)"
                      ></wd-icon>
                    </view>
                    <wd-text :text="item.telephone" mode="phone" :format="true"></wd-text>
                  </view>
                </view>
                <view>
                  <wd-checkbox :modelValue="item.id"></wd-checkbox>
                </view>
              </view>
            </view>
          </wd-checkbox-group>
          <view style="padding-top: 40rpx" v-else>
            <wd-status-tip image="content" tip="暂无人员" />
          </view>
        </scroll-view>
      </view>

      <view class="px-4">
        <wd-button
          custom-style="border-radius:4px;width:100%"
          type="primary"
          @click="openPage()"
          icon="add"
          plain
        >
          添加人员
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup>
import { ref } from 'vue'
const show = ref(false)
const lists = ref([])
import { getClientList } from '../../api/index.js'
const formData = ref([])
const openShow = async (e) => {
  console.log(e, 'e')
  show.value = true
  formData.value = e ? [e] : []
  await getList()
}
const emit = defineEmits('change')
const onChange = (e) => {
  if (e.value && e.value.length) {
    const data = e.value.pop()
    formData.value = [data]
    show.value = false
    emit(
      'change',
      lists.value.find((item) => item.id === data),
    )
  } else {
    formData.value = []
  }
}
const openPage = (data) => {
  uni.navigateTo({
    url: data
      ? `/pagesHotel/pages/hotel/user/index?id=${data.id}&data=${JSON.stringify(data)}`
      : `/pagesHotel/pages/hotel/user/index`,
    events: {
      reload: () => {
        getList()
      },
    },
  })
}

async function getList() {
  const { data } = await getClientList()
  lists.value = data || []
}

defineExpose({
  openShow,
})
</script>

<style scoped lang="scss">
.car-tag-1 {
  background-color: #ecf0f4;
  color: #a2adba;
}
.car-tag-2 {
  background-color: #d3f6e6;
  color: #06bb67;
}
.car-tag-3 {
  background-color: #fff1d7;
  color: #ffae17;
}
.car-tag-4 {
  background-color: #ffe6e6;
  color: #ff5f5f;
}
.card-body {
  margin: 0 32rpx;
  // box-shadow: 0px 2rpx 20rpx rgb(51 54 74 / 5%);
  padding: 30rpx;
  border: 2rpx solid #f5f5f5;
  background: #fafafa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  .car-tag {
    width: fit-content;
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    padding: 10rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
  }
  .top-body {
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
  }
  .car-icon {
    width: 50rpx;
    height: 44rpx;
    padding-right: 20rpx;
  }
  .left-body {
    align-items: center;
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
  }
}

.button-disabled {
  background: #ccc !important;
}
.button-class {
  background: #1285ff;
  text-align: center;
  color: #fff;
  font-weight: 500;
  margin: 0 30rpx;
  font-size: 32rpx;
  border-radius: 8rpx;
  padding: 16rpx;
}
.item-map-pupn {
  height: 30rpx;
  width: 30rpx;
}
.popup-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  .popup-title {
    width: 100%;
    font-weight: bold;
    text-align: center;
  }
  .popup-list {
    flex: 1 1 0;
    font-size: 26rpx;
    overflow-y: hidden;
    padding-bottom: 30rpx;
    padding-top: 20rpx;
  }
  .popup-name {
    display: flex;
    align-items: center;
    color: #1285ff;
  }
  .item-body {
    padding: 30rpx 40rpx;
    margin: 20rpx 0;
    background: #f8f8f8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.item-line {
  padding: 15rpx 0;
}
.item-map {
  height: 30rpx;
  width: 30rpx;
  padding-right: 10rpx;
}
</style>
