<template>
  <wd-popup
    v-model="show"
    position="bottom"
    :safe-area-inset-bottom="true"
    custom-style="padding: 30rpx 0rpx;height:60%;min-height:400rpx;border-radius: 32rpx 32rpx 0 0;"
  >
    <view class="popup-body">
      <view class="popup-title">在线选房</view>
      <view class="popup-list">
        <wd-picker
          label="房间编号"
          required
          size="large"
          placeholder="请选择房间编号"
          v-model="formData"
          :columns="list"
        />
        <!-- <view class="flex mx-4 mt-4 items-center justify-between py-3 border-b border-gray-100">
          <text class="text-sm text-gray-800">
            <text class="text-#244190">*</text>
            房间编号
          </text>
          <view class="flex items-center">
            <text class="text-sm text-gray-800 mr-2">请选择房间编号</text>
            <wd-icon name="arrow-right" size="16px" color="#999" />
          </view>
        </view> -->
      </view>

      <view class="px-4">
        <wd-button custom-style="border-radius:4px;width:100%" type="primary" plain>确定</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup>
import { ref } from 'vue'
const show = ref(false)
const lists = ref([])
import { getAvaliableRoomNums } from '../../api/index.js'
const formData = ref()
const params = ref({
  room_type: '',
  hotel_id: '',
  start_date: '',
  end_date: '',
})
const openShow = async (e) => {
  params.value.room_type = e.room_type
  params.value.hotel_id = e.hotel_id
  params.value.start_date = e.start_date
  params.value.end_date = e.end_date
  show.value = true
  await getList()
}
const emit = defineEmits('change')
const onChange = (e) => {
  if (e.value && e.value.length) {
    const data = e.value.pop()
    formData.value = [data]
    show.value = false
    emit(
      'change',
      lists.value.find((item) => item.id === data),
    )
  } else {
    formData.value = []
  }
}

async function getList() {
  const { data } = await getAvaliableRoomNums(params.value)
  lists.value = data || []
}

defineExpose({
  openShow,
})
</script>

<style scoped lang="scss">
.popup-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  .popup-title {
    width: 100%;
    font-weight: bold;
    text-align: center;
  }
  .popup-list {
    flex: 1 1 0;
    font-size: 26rpx;
    overflow-y: hidden;
    padding-bottom: 30rpx;
    padding-top: 20rpx;
  }
  .popup-name {
    display: flex;
    align-items: center;
    color: #1285ff;
  }
  .item-body {
    padding: 30rpx 40rpx;
    margin: 20rpx 0;
    background: #f8f8f8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
