<template>
	<wd-overlay :show="show" @click="onShow" :zIndex="99">
		<view class="wrapper-events">
			<view class="block" @click.stop="">
				<view style="padding: 0 80rpx; position: relative">
					<swiper style="height: 760rpx; max-height: 760rpx" :indicator-dots="swiperList.length > 1" indicator-active-color="#fff">
						<swiper-item v-for="items in swiperList" :key="items.id">
							<view style="padding: 0 10rpx">
								<image class="image-events" mode="widthFix" :src="items.image" @click.stop="onLink(items)"></image>
							</view>
						</swiper-item>
					</swiper>
					<!--  <view style="padding-top: 10rpx; position: absolute; top: 0; right: 85rpx">
                        <wd-icon name="close-normal" size="22px" color="#fff" @click="onShow"></wd-icon>
                    </view> -->
				</view>

				<view style="display: flex; justify-content: center">
					<wd-icon name="close-outline" size="35px" :color="'#fff'" @click="onShow"></wd-icon>
				</view>
			</view>
		</view>
	</wd-overlay>
</template>
<script setup>
import { getList } from './api/index.js';
import { onChange } from '/src/utils/open-menu.js';
import { computed, onMounted, ref } from 'vue';
import { useUser } from '/src/utils/hook/useUser.js';
const { isLogin, setLogin, user } = useUser();
const props = defineProps({
	position: {
		type: String,
		default: ''
	}
});
// onChange(items)
const show = ref(false);
const onShow = (e) => {
	show.value = false;
};

const onLink = (e) => {
	if (e.deploy && e.deploy.type == 'coupon' && !isLogin.value) {
		setLogin();
		return;
	}
	if (!e.deploy || e.deploy.type != 'coupon') {
		show.value = false;
	}
	return onChange(e);
};

const swiperList = ref([]);
const openPage = (e) => {
	if (e.item.url) {
		uni.navigateTo({
			url: e.item.url
		});
	}
};
const initData = () => {
	getList({ position: props.position }).then((res) => {
		swiperList.value = res.data;
		if (swiperList.value && swiperList.value.length) {
			show.value = true;
		}
	});
};
onMounted(() => {
	initData();
});
</script>

<style lang="scss" scoped>
.wrapper-events {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	position: relative;
	.block {
		width: 100%;
		.image-events {
			width: 100%;
			max-height: 760rpx;
			height: 100%;

			border-radius: 16rpx 16rpx 16rpx 16rpx;
		}
	}
}
</style>
