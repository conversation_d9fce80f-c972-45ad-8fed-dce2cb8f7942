<template>
	<view class="list-bodys" v-if="swiperList && swiperList.length">
		<view class="lg-flex top-item">
			<view class="item-name">
				<image src="/static/biaoji.png" class="item-icon"></image>
				专属推荐
			</view>
		</view>
		<view class="list-card-body">
			<scroll-view scroll-x>
				<view class="lg-flex" style="padding-bottom: 20rpx">
					<view v-for="item in swiperList" :key="item.id">
						<view class="image-item">
							<image :src="item.image" @click="onChange(item)" class="imge-class"></image>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { getList } from './api/index.js';
import { onChange } from '/src/utils/open-menu.js';
import { onPullDownRefresh } from '@dcloudio/uni-app';
const props = defineProps({
	position: {
		type: String,
		default: ''
	}
});
const swiperList = ref([]);
onPullDownRefresh(() => {
	initData();
});
const initData = () => {
	getList({ position: props.position }).then((res) => {
		swiperList.value = res.data;
	});
};
onMounted(() => {
	initData();
});
</script>

<style scoped lang="scss">
.list-bodys {
	padding: 10rpx 30rpx;
	.top-item {
		justify-content: space-between;
	}
	.image-item {
		width: 316rpx;
		height: 180rpx;
		margin-right: 20rpx;
		border-radius: 16rpx;
		.imge-class {
			width: 100%;
			height: 100%;
			border-radius: 16rpx;
		}
	}

	.item-map {
		height: 30rpx;
		width: 30rpx;
		padding-right: 10rpx;
	}

	.item-name {
		display: flex;
		align-items: center;
		font-weight: bold;
		font-size: 32rpx;
		.item-icon {
			height: 30rpx;
			width: 50rpx;
			padding-right: 10rpx;
		}
	}
	.list-card-body {
		margin-top: 20rpx;
	}
}
</style>
