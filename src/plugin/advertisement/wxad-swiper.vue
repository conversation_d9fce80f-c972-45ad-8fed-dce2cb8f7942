<!-- 
 * 广告轮播插件
 * @author: 张学勇
 * @date: 2024-12-09 11:15:22
 * @description:
 -->
<template>
	<!-- #ifdef MP-WEIXIN -->
	<view class="at-swiper" :style="{ paddingTop: paddingTop, paddingBottom: paddingBottom, paddingLeft: paddingLeft, paddingRight: paddingRight }">
		<view style="width: 100%">
			<ad-custom v-if="logding" :unit-id="unitId" @bindload="adLoad" @binderror="adError"></ad-custom>
		</view>
	</view>
	<!-- #endif -->
</template>

<script setup>
import { computed, onMounted, nextTick, ref } from 'vue';
const props = defineProps({
	unitId: {
		type: String,
		default: ''
	},
	paddingTop: {
		type: String,
		default: '0rpx'
	},
	paddingBottom: {
		type: String,
		default: '0rpx'
	},
	paddingLeft: {
		type: String,
		default: '30rpx'
	},
	paddingRight: {
		type: String,
		default: '30rpx'
	}
});
const logding = ref(false);
onMounted(() => {
	nextTick(() => {
		setTimeout(() => {
			logding.value = true;
		}, 1500);
	});
});
const adLoad = (e) => {
	console.log(e);
};
const adError = (e) => {
	console.log(e);
};
</script>

<style lang="scss" scoped></style>
