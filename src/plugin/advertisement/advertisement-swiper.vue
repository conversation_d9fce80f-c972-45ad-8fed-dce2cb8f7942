<!-- 
 * 广告轮播插件
 * @author: 张学勇
 * @date: 2024-12-09 11:15:22
 * @description:
 -->
<template>
  <view
    class="at-swiper"
    :style="{
      paddingTop: paddingTop,
      paddingBottom: paddingBottom,
      paddingLeft: paddingLeft,
      paddingRight: paddingRight,
    }"
    v-if="swiperList && swiperList.length"
  >
    <wd-swiper
      value-key="image"
      @click="(e) => onChange(e.item)"
      :list="swiperList"
      autoplay
      :display-multiple-items="displayMultipleItems"
      :height="height"
      :custom-indicator-class="customIndicatorClass"
      :custom-class="customClass"
      :customItemClass="customItemClass"
      :previous-margin="previousMargin"
      :next-margin="nextMargin"
      :indicator="indicator"
      :custom-next-image-class="customNextImageClass"
      :custom-prev-image-class="customPrevImageClass"
      :custom-image-class="customImageClass"
    ></wd-swiper>
  </view>
</template>

<script setup>
import { getList } from './api/index.js'
import { onChange } from '/src/utils/open-menu.js'
import { computed, onMounted, ref } from 'vue'
import { onPullDownRefresh } from '@dcloudio/uni-app'
const props = defineProps({
  displayMultipleItems: {
    type: Number,
    default: 0,
  },
  customImageClass: {
    type: String,
    default: '',
  },
  customNextImageClass: {
    type: String,
    default: '',
  },
  customPrevImageClass: {
    type: String,
    default: '',
  },
  previousMargin: {
    type: String,
    default: '0rpx',
  },
  nextMargin: {
    type: String,
    default: '0rpx',
  },
  indicator: {
    type: Object,
    default: () => ({}),
  },

  customItemClass: {
    type: String,
    default: '',
  },
  customClass: {
    type: String,
    default: '',
  },
  customIndicatorClass: {
    type: String,
    default: '',
  },
  height: {
    type: Number,
    default: 200,
  },
  position: {
    type: String,
    default: '',
  },
  paddingTop: {
    type: String,
    default: '0rpx',
  },
  paddingBottom: {
    type: String,
    default: '0rpx',
  },
  paddingLeft: {
    type: String,
    default: '0rpx',
  },
  paddingRight: {
    type: String,
    default: '0rpx',
  },
})
const swiperList = ref([])
onPullDownRefresh(() => {
  initData()
})
const initData = () => {
  getList({ position: props.position }).then((res) => {
    swiperList.value = res.data
  })
}
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped></style>
