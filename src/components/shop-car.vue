<template>
  <wd-fab position="right-bottom" :expandable="false" :draggable="true">
    <template #trigger>
      <wd-badge :modelValue="totalCount" right="10rpx" top="10rpx">
        <wd-img @click="goToCart" src="/static/shop/shop-car.png" width="100rpx" height="100rpx" />
      </wd-badge>
    </template>
  </wd-fab>
</template>
<script lang="ts" setup>
import { useShopCar } from '@/utils/hook/useShopCar'
import { computed } from 'vue'

const { shopCarList } = useShopCar()

const totalCount = computed(() => {
  return shopCarList.value.reduce((acc, item) => acc + item.count, 0)
})

const goToCart = () => {
  uni.navigateTo({
    url: '/pagesShop/pages/shop-cart/index',
  })
}
</script>
