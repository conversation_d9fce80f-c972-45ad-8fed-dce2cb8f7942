<template>
  <wd-calendar
    ref="calendarRef"
    type="daterange"
    v-model="value"
    placeholder="请选择日期"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :formatter="formatter"
    :inner-display-format="innerDisplayFormat"
    :min-date="new Date()"
    :with-cell="false"
  />
</template>

<script setup>
import { ref, watch } from 'vue'
import { dayjs } from '@/uni_modules/wot-design-uni/components/common/dayjs'
import { getDaysDiff } from '@/utils/utils'

const value = ref([])

const visible = defineModel('visible')

const emit = defineEmits(['confirm'])

const calendarRef = ref(null)
watch(visible, (newVal) => {
  if (newVal) {
    calendarRef.value.open()
  } else {
    calendarRef.value.close()
  }
})

const handleConfirm = ({ value }) => {
  emit('confirm', {
    start: dayjs(value[0]).format('YYYY-MM-DD'),
    end: dayjs(value[1]).format('YYYY-MM-DD'),
    interval: getDaysDiff(value[0], value[1]),
  })
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}

const innerDisplayFormat = (value, rangeType) => {
  if (!value) {
    return rangeType === 'start' ? '入住' : '离店'
  }

  return dayjs(value).format('YY年MM月DD日')
}

const formatter = (day) => {
  const date = new Date(day.date)
  const now = new Date()
  const year = date.getFullYear()
  const month = date.getMonth()
  const da = date.getDate()
  const nowYear = now.getFullYear()
  const nowMonth = now.getMonth()
  const nowDa = now.getDate()

  if (year === nowYear && month === nowMonth && da === nowDa) {
    day.topInfo = '今天'
  }

  if (day.type === 'start') {
    day.bottomInfo = '入住'
  }

  if (day.type === 'end') {
    day.bottomInfo = '离店'
  }

  if (day.type === 'same') {
    day.bottomInfo = '入住/离店'
  }

  return day
}
</script>

<style lang="scss" scoped></style>
