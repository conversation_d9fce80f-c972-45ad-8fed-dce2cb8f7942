<template>
    <wd-calendar type="datetimerange"
	 
	 :display-format="displayFormat"
	 :modelValue="yearMonth" :maxDate="maxTime" :minDate="minTime" @update:modelValue="onChange" />
</template>

<script setup>
import { computed } from 'vue';
import { lg } from '/src/utils/utils.js';
const props = defineProps({
    modelValue: {
        type: [Array],
        default() {
            return [];
        }
    },
    minDate: {
        type: null,
        default() {
            return null;
        }
    },
    maxDate: {
        type: null,
        default() {
            return null;
        }
    }
});
const displayFormat=(e)=>{
	let startTime = lg.dayjs(e[0]).format('YYYY-MM-DD HH:mm');
	let endTime = lg.dayjs(e[1]).format('YYYY-MM-DD HH:mm');
	return `${startTime} ~ ${endTime}`
}
const yearMonth = computed(() => {
    if (props.modelValue && props.modelValue.length) {
        let startTime = new Date(lg.dayjs(props.modelValue[0])).getTime();
        let endTime = new Date(lg.dayjs(props.modelValue[1])).getTime();
        return [startTime, endTime];
    } else {
        return [];
    }
});
const maxTime = computed(() => {
    if (props.maxDate) {
        return new Date(lg.dayjs(props.maxDate)).getTime();
    } else {
        return new Date(new Date().getFullYear(), new Date().getMonth() + 6, new Date().getDate(), 23, 59, 59).getTime();
    }
});
const minTime = computed(() => {
    if (props.minDate) {
        return new Date(lg.dayjs(props.minDate)).getTime();
    } else {
        return new Date(new Date().getFullYear(), new Date().getMonth() - 6, new Date().getDate()).getTime();
    }
});
const emit = defineEmits(['update:modelValue', 'change']);
const onChange = (e) => {
    if (e && e.length) {
        let startTime = lg.dayjs(e[0]).format('YYYY-MM-DD HH:mm');
        let endTime = lg.dayjs(e[1]).format('YYYY-MM-DD HH:mm');
        emit('update:modelValue', [startTime, endTime]);
        emit('change', [startTime, endTime]);
    }
};
</script>
