<template>
	<view class="gradient-bg page-navbar-buttom" :class="[centre ? 'page-navbar-centre' : '']" :style="styleClass">
		<view class="icon-class" v-if="back" @click="openBack"><wd-icon name="arrow-left" size="22px"></wd-icon></view>
		<view class="page-navbar-title" :class="[centre ? 'title-content' : 'title-left']">{{ title }}</view>
		<view style="width: 80rpx" v-if="back"></view>
	</view>
	<view :style="styleClass" class="page-navbar-buttom"></view>
</template>
<script>
export default {
	options: {
		styleIsolation: 'shared'
	}
};
</script>
<script setup>
import { onLoad, onPageScroll } from '@dcloudio/uni-app';
import { onMounted, reactive,computed } from 'vue';
const props = defineProps({
	title: {
		type: String,
		default: ''
	},
	centre: {
		type: Boolean,
		default: true
	},
	back: {
		type: Boolean,
		default: true
	},
	background: {
		type: Boolean,
		default: false
	},
	scroll: {
		type: Boolean,
		default: false
	},
	customStyle:{
		type: Object,
		default:()=>{
			return {}
		}
	}
});
const data = reactive({
	menuButtonInfo: '',
	styleList: {},
	title: ''
});
const styleClass= computed(()=>{
	return {...data.styleList,...props.customStyle}
})

// 返回上一页
const openBack = () => {
	// saveCurrentPageBackStatus(true)
	uni.navigateBack({
		delta: 1,
		fail: () => {
			uni.reLaunch({
				url: '/pages/index/index'
			});
			// console.log('跳转失败');
		}
	});
};

// 获取胶囊高度
onLoad(() => {
	// #ifdef MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ
	data.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
	data.styleList = { paddingTop: data.menuButtonInfo.top + 'px', height: data.menuButtonInfo.height + 'px' };
	checkBackground();
	// #endif
});
onMounted(() => {
	// #ifdef  MP-ALIPAY
	data.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
	data.styleList = { paddingTop: data.menuButtonInfo.top + 'px', height: data.menuButtonInfo.height + 'px', paddingLeft: ' 80rpx' };
	checkBackground();
	// #endif
	// #ifdef  WEB||H5
	checkBackground();
	// #endif
});

const checkBackground = () => {
	if (props.background) {
		data.styleList = {
			...data.styleList,
			background: 'rgba(0,0,0,0)',
			color: '#fff'
		};
	}
};
</script>

<style lang="scss" scoped>
.title-content {
	text-align: center;
}
.title-left {
	padding-left: 30rpx;
}
.page-navbar-title {
	flex: 1;

	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.page-navbar-centre {
	justify-content: space-between;
}
.icon-class {
	width: 80rpx;
	text-align: center;
}
.page-navbar-buttom {
	height: calc(44px + env(safe-area-inset-top));
	padding-bottom: 20rpx;
}
.gradient-bg {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	z-index: 99;
	background: linear-gradient(to bottom, rgba(214, 242, 241, 1), rgba(239, 247, 247, 1));
	width: 100%;
	display: flex;
	align-items: center;
	font-size: 30rpx;
	font-weight: bold;
}
</style>
