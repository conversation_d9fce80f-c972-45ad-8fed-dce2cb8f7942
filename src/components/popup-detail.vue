<template>
  <wd-popup
    v-model="show"
    position="bottom"
    :safe-area-inset-bottom="true"
    custom-style="padding: 30rpx 10rpx;height:70%;min-height:400rpx;border-radius: 32rpx 32rpx 0 0;"
  >
    <view class="popup-body">
      <view class="popup-title">优惠券</view>
      <view class="popup-list">
        <scroll-view :scroll-y="true" style="height: 100%">
          <wd-checkbox-group
            checked-color="#FF8E5E"
            :modelValue="datas"
            size="large"
            inline
            v-if="lists.length"
          >
            <!-- 优惠券卡片列表 -->
            <view v-for="item in lists" :key="item.id" class="mx-3 mt-3 relative">
              <!-- 优惠券卡片 - 使用背景图片 -->
              <view
                class="relative w-full h-240rpx bg-cover bg-center bg-no-repeat"
                :style="{
                  backgroundImage: `url('${
                    totalAmount < parseFloat(item.doorsill_money)
                      ? '/static/components/isuse-border.png'
                      : '/static/components/unuse-border.png'
                  }')`,
                  backgroundSize: '100% 100%',
                  backgroundRepeat: 'no-repeat',
                }"
                @click="onChange(item)"
              >
                <!-- 优惠券内容区域 -->
                <view class="flex h-full px-2">
                  <!-- 左侧优惠券面额区域 -->
                  <view
                    class="flex-shrink-0 w-220rpx flex flex-col items-center justify-center"
                    :class="
                      totalAmount < parseFloat(item.doorsill_money)
                        ? 'text-#999999'
                        : 'text-#244190'
                    "
                    @click.stop="onDetalis(item)"
                  >
                    <!-- 优惠券面额 -->
                    <view class="flex items-end justify-center">
                      <text class="text-54rpx font-bold leading-none">
                        {{ item.type == '3' ? item.discount : item.money }}
                      </text>
                      <text class="text-24rpx">
                        {{ item.type == '3' ? `折` : '元' }}
                      </text>
                    </view>
                    <text class="text-24rpx mt-1">
                      {{ item.type == '2' ? `满${item.doorsill_money}可用` : '' }}
                    </text>
                  </view>

                  <!-- 右侧优惠券信息区域 -->
                  <view class="flex-1 py-4 flex flex-col justify-center relative">
                    <view
                      class="border-#E9E9E9 absolute w-2rpx top-15% left--3 h-70% border-dashed border-l border-b-none border-t-none border-r-none"
                    ></view>
                    <!-- 优惠券标题 -->
                    <text
                      class="text-36rpx font-medium mb-2 block leading-tight"
                      :class="
                        totalAmount < parseFloat(item.doorsill_money)
                          ? 'text-#999999'
                          : 'text-#244190'
                      "
                    >
                      {{ couponTypeMap[item.type] || item.title }}
                    </text>

                    <!-- 适用场景 -->
                    <text
                      class="text-26rpx mb-2 block"
                      :class="
                        totalAmount < parseFloat(item.doorsill_money)
                          ? 'text-#999999'
                          : 'text-#244190'
                      "
                    >
                      适用范围：{{ item.is_range ? item.car_park_name.join(',') : '全场通用' }}
                    </text>

                    <!-- 有效期 -->
                    <text
                      class="text-24rpx block"
                      :class="
                        totalAmount < parseFloat(item.doorsill_money)
                          ? 'text-#999999'
                          : 'text-#244190'
                      "
                    >
                      有效期：{{ item.start_date }}至{{ item.end_date }}
                    </text>
                  </view>

                  <!-- 右侧选择框和详情按钮区域 -->
                  <view
                    class="flex flex-col items-center justify-center space-y-2 px-2"
                    v-if="totalAmount >= parseFloat(item.doorsill_money)"
                  >
                    <!-- 选择框 -->
                    <wd-checkbox :modelValue="item.id" @click.stop=""></wd-checkbox>
                  </view>
                </view>

                <!-- 状态标记 -->
                <view
                  v-if="item.state === '2' || item.state === '3' || item.state === '4'"
                  class="absolute top-1/2 right-8 transform -translate-y-1/2 z-10"
                >
                  <image
                    :src="getStateImage(item.state)"
                    class="w-160rpx h-160rpx"
                    mode="aspectFit"
                  />
                </view>
              </view>
            </view>
          </wd-checkbox-group>

          <wd-status-tip image="content" tip="暂无可用优惠券" v-else />
        </scroll-view>
      </view>
    </view>
  </wd-popup>

  <wd-overlay :show="overlayShow" @click="overlayShow = false">
    <view class="wrapper">
      <view class="block" @click.stop="">
        <view class="wrapper-title">{{ details.title || couponTypeMap[details.type] }}</view>
        <view>
          <view class="item-line">
            金额：{{ details.type == '3' ? details.discount + '折' : details.money + '元' }}
          </view>
          <view class="item-line" v-if="details.type == '2'">
            使用门槛：满{{ details.doorsill_money }}可用
          </view>
          <view class="item-line">有效期：{{ details.start_date }}至{{ details.end_date }}</view>
          <view class="item-line">
            适用范围：{{ details.is_range ? details.car_park_name.join(',') : '全场通用' }}
          </view>
          <view class="item-line" v-if="details.scene">
            适用场景：{{ sceneTypeMap[details.scene] }}
          </view>
          <!-- <view class="item-line">状态：{{ couponStatusMap[details.state] }}</view> -->
        </view>
      </view>
      <wd-icon name="close-outline" size="35px" :color="'#fff'"></wd-icon>
    </view>
  </wd-overlay>
</template>
<script>
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script setup>
import { ref, watch } from 'vue'

// 引入统一的数据映射
const sceneTypeMap = {
  1: '线上订房',
  2: '购买商品',
  3: '购买套餐',
}

const couponTypeMap = {
  1: '代金券',
  2: '满减券',
  3: '折扣券',
}

const couponStatusMap = {
  1: '未使用',
  2: '已使用',
  3: '已过期',
  4: '已失效',
}

const show = ref(false)
const overlayShow = ref(false)
const datas = ref([])
const lists = ref([])
const details = ref({})

// 获取优惠券背景图片 (1:未使用,2:已使用,3:已过期,4:已失效)
const getCouponBgImage = (doorsill_money) => {
  if (totalAmount > parseFloat(doorsill_money)) {
    return '/static/components/isuse-border.png'
  }
  return '/static/components/unuse-border.png'
}

// 获取状态图片
const getStateImage = (state) => {
  if (state === '2') {
    return '/pagesMy/static/is-use.png'
  } else if (state === '3' || state === '4') {
    return '/pagesMy/static/is-expired.png'
  }
  return ''
}

const onDetalis = (e) => {
  details.value = e
  overlayShow.value = true
}
const totalAmount = ref(9999999)
const openShow = (e, total) => {
  lists.value = e
  totalAmount.value = total || 0
  show.value = true
}
const emit = defineEmits(['change'])

const onChange = (e) => {
  if (totalAmount.value < parseFloat(e.doorsill_money)) {
    uni.showToast({
      title: '该优惠券不可用',
      icon: 'none',
    })
    return
  }
  if (e.id == datas.value[0]) {
    datas.value = []
    emit('change', '', {})
  } else {
    datas.value = [e.id]
    emit('change', e.id, e)
  }
  show.value = false
}
defineExpose({
  openShow,
})
</script>

<style scoped lang="scss">
:deep(.wd-checkbox__label) {
  margin-left: 0 !important;
}
:deep(.is-inline) {
  margin-right: 0 !important;
}

.wrapper {
  margin-top: 400rpx;
  text-align: center;
  .block {
    padding: 0 45rpx 42rpx;
    height: auto;
    margin: 0 30rpx;
    margin-bottom: 30rpx;
    min-height: 400rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    text-align: left;
    .wrapper-title {
      text-align: center;
      font-weight: bold;
      font-size: 32rpx;
      color: #333333;
      padding: 32rpx 0 36rpx;
    }
    .item-line {
      padding: 10rpx 0;
    }
  }
}
.popup-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  .popup-title {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  .popup-list {
    flex: 1;
    overflow-y: hidden;
    padding-bottom: 30rpx;
  }
}
</style>
