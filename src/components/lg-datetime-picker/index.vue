<template>
    <wd-datetime-picker
        align-right
        type="date"
        :maxDate="maxTime"
        :default-value="maxTime"
        :minDate="minTime"
        :modelValue="yearMonth"
        @update:modelValue="onChange"
    />
</template>

<script setup>
import { computed } from 'vue';
import { lg } from '/src/utils/utils.js';
const props = defineProps({
    modelValue: {
        type: [String],
        default: ''
    },
    minDate: {
        type: null,
        default() {
            return null;
        }
    },
    maxDate: {
        type: null,
        default() {
            return null;
        }
    }
});

const yearMonth = computed(() => {
    if (props.modelValue) {
        return new Date(lg.dayjs(props.modelValue)).getTime();
    } else {
        return '';
    }
});
const maxTime = computed(() => {
    if (props.maxDate) {
        return new Date(lg.dayjs(props.maxDate)).getTime();
    } else {
        return new Date(new Date().getFullYear() + 10, 11, 31).getTime();
    }
});
const minTime = computed(() => {
    if (props.minDate) {
        return new Date(lg.dayjs(props.minDate)).getTime();
    } else {
        return new Date(new Date().getFullYear() - 10, 0, 1).getTime();
    }
});
const emit = defineEmits(['update:modelValue', 'change']);
const onChange = (e) => {
    let time = lg.dayjs(e).format('YYYY-MM-DD');
    emit('update:modelValue', time);
    emit('change', time);
};
</script>

<style></style>
