<template>
	<view>
		<wd-status-tip image="content" tip="暂无数据" v-if="data.isShow&&isEmpty" />
		<wd-loadmore v-if="isLoadmore" :state="data.state" :finished-text="data.finishedText" @reload="onReload" />
	</view>
</template>
<script>
export default {
	name: 'lgApi'
};
</script>
<script setup>
import { onReachBottom, onShow } from '@dcloudio/uni-app';
import { getCurrentInstance,ref, reactive, nextTick, computed, onMounted, inject, onBeforeMount, onBeforeUnmount } from 'vue';
const queue = ref(null)
const { proxy } = getCurrentInstance();
const props = defineProps({
	listApi: {
		type: [Boolean, Function],
		default: false
	},
	afterData: {
		type: [Boolean, Function],
		default: false
	},
	limit: {
		type: Number,
		default: 20
	},
	noData: {
		type: Boolean,
		default: false
	},
	type: {
		type: String,
		default: 'page'
	},
	apiName:{
	type: String,
	default: 'lgapi'	
	},
	isEmpty: {
		type: Boolean,
		default: true
	},
	sieve: {
		type: [Boolean, Object],
		default: false
	},
	
});
const data = reactive({
	loading: false,
	page: 1,
	total: 1,
	loadend: false,
	isShow: false,
	state: 'finished',
	finishedText: '上拉加载更多'
});
const emit = defineEmits(['change', 'error']);

// 是否显示加载
const isLoadmore = computed(() => {
	// 如果为空则不显示
	if (data.isShow) {
		return false;
	}
	// 不在加载中且小于6则不显示
	if (!data.loading && data.total < 6) {
		return false;
	}
	return true;
});

// 重置组件数据
const initialize = () => {
	data.loading = true;
	data.page = 1;
	data.total = 1;
	data.loadend = false;
	data.state = 'finished';
	data.finishedText = '上拉加载更多';
};

/**
 * 初始化加载数据
 * @author: 张学勇
 * @date: 2024-04-07 11:11:13
 * @param e 筛选值
 */
const initData = (e = {}) => {
	if (props.listApi) {
		data.state = 'loading';
		let sieve = { current: data.page, size: props.limit, ...e };
		
		if(props.sieve){
			sieve={...sieve,...props.sieve}
		}
		if (props.afterData) {
			sieve = props.afterData(sieve);
		}
		data.loading = true;
		data.isShow = false;
		props
			.listApi(sieve)
			.then((res) => {
				const datas = res.data;
				data.total = datas.total;
				if (datas.current == 1 && (!datas.records || !datas.records.length)) {
					data.isShow = true;
				}
				data.page = datas.current + 1;
				data.loadend = datas.records.length < props.limit;
				data.state = 'finished';
				data.finishedText = data.loadend ? '没有更多了' : '上拉加载更多';
				emit('change', datas);
			})
			.catch((error) => {
				data.isShow = true;
				data.state = 'error';
				emit('error', error);
			})
			.finally(() => {
				data.loading = false;
			});
	}
};
const initBottom=()=>{
	if (data.loadend || data.loading) return;
	initData();
}
// 点击重新加载
const onReload = () => {
	initData();
};
defineExpose({
	initData,
	initialize,
	initBottom
});

// 触底加载
onReachBottom(() => {
	initBottom()
});

onMounted(() => {
	if (props.type == 'component') {
		nextTick(() => {
			if (!props.noData) {
				initialize();
				initData();
			}
		});
	}
});
// 显示加载
onShow(async () => {
	if (props.type == 'page') {
		await nextTick();
		if (!props.noData) {
			initialize();
			initData();
		}
	}
});

onBeforeMount(() => {
	 queue.value = inject(props.apiName, null);
	if (queue.value && queue.value.pushApi) {
		queue.value.pushApi(proxy);
	}
});
onBeforeUnmount(() => {
	 queue.value = inject(props.apiName, null);
	if (queue.value && queue.value.removeFromApi) {
		queue.value.removeFromApi(proxy);
	}
});
</script>
