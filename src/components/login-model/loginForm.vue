<template>
	<view class="login-buttom">
		<view style="padding: 50rpx 0">
			<wd-button
				block
				:open-type="data.authorize && !data.disabled ? 'getPhoneNumber' : ''"
				@click="judgePhoneNumber"
				:loading="data.loading"
				@getphonenumber="getPhoneNumber"
			>
				手机号快速登录
			</wd-button>
		</view>
		<view class="buttom-agreement lg-flex">
			<wd-checkbox v-model="data.authorize" inline size="large" checked-color="#1285FF"></wd-checkbox>
			我已认真阅读并同意
			<text style="color:#1285FF" @click="onAgreement('employ')">《用户协议》</text>
			和
			<text style="color:#1285FF" @click="onAgreement('privacy')">《隐私协议》</text>
		</view>
	</view>
</template>

<script setup>
import loginForm from './loginForm.vue';
import { reactive, watch, nextTick,onMounted } from 'vue';
import { getLogin } from '/src/api/unify.js';
import { useUser } from '/src/utils/hook/useUser.js';
import { timeFrom } from '/src/utils/utils.js';
const { initUserInfo } = useUser();
const props = defineProps({
	verify: {
		type: Boolean,
		default: false
	}
});
const emit = defineEmits(['change']);
const data = reactive({
	authorize: true,
	disabled: false,
	loading: false,
	openid: ''
});

onMounted(() => {
	judgeLogin();
});

/**
 * 触发微信手机号登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 15:42:27
 * @param
 * @return
 */
const getPhoneNumber = (e) => {
	if (e.code) {
		// #ifdef  MP-WEIXIN
		wx.login({
			success: function (res) {
				setLogin({
					codePhone: e.code,
					code: res.code,
				});
			},
			fails: function () {
				toast('获取用户消息失败,无法登录')
			}
		});
		//#endif
	} else {
		toast('无法获取手机号码')
	}
};

/**
 * 处理登录逻辑
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 15:42:02
 * @param
 * @return
 */
const setLogin = (e) => {
	data.loading = true;
	getLogin(e)
		.then((res) => {
			uni.setStorageSync('tm_token', res.data.token);
			emit('change', true);
			toast('登录成功')
			initUserInfo();
		})
		.finally(() => {
			data.loading = false;
		});
};

/**
 * 判断是否恶意进行手机号登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 15:51:13
 * @param
 * @return
 */
const judgePhoneNumber = () => {
	if (!data.authorize) {
		toast('请阅读并勾选用户协议')
	} else {
		const click_data = uni.getStorageSync('click_login');
		if (click_data && click_data.sum > 40) {
			data.disabled = true;
			toast('请求过于频繁，请1小时后重试')
		} else if (click_data && click_data.sum > 39) {
			uni.setStorageSync('click_login', {
				date: Date.parse(new Date()),
				sum: click_data.sum + 1
			});
			data.disabled = true;
		} else {
			uni.setStorageSync('click_login', {
				date: Date.parse(new Date()),
				sum: click_data.sum + 1
			});
		}
	}
};

/**
 * 检验是否可以使用手机号登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 16:19:04
 * @param
 * @return
 */
const judgeLogin = () => {
	const click_data = uni.getStorageSync('click_login');
	if (click_data && click_data.sum > 40) {
		if (timeFrom(click_data.date)) {
			uni.setStorageSync('click_login', {
				date: Date.parse(new Date()),
				sum: 1
			});
		} else {
			data.disabled = true;
		}
	}
};

const toast=(e)=>{
	uni.showToast({
		icon: 'none',
		title: e,
		duration: 2000
	});
}

// 查看协议
const onAgreement = (e) => {
	if (!data.loading) {
		uni.navigateTo({
			url: `/pages/my/system-manage/protocol-details/index?name=${e}`
		});
	}
};
</script>
<style lang="scss" scoped>
.login-buttom {
	margin-top: 30rpx;
	:deep(.is-success) {
		background-color: #19be6b !important;
	}
	.buttom-agreement {
		padding: 30rpx 0 0 0;
		text-align: center;
		font-size: 26rpx;
		color: #909399;
		font-weight: bold;
	}
}
</style>