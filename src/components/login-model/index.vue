<template>
  <view>
    <wd-popup
      :z-index="110"
      v-model="data.show"
      position="bottom"
      custom-style="height: 35%;"
      :safe-area-inset-bottom="true"
      :close-on-click-modal="!data.loading"
    >
      <view class="login-model">
        <view class="lg-flex top-logo">
          <image src="/static/logo.jpeg" class="logo-img" mode="widthFix"></image>
          CHCC长河星球
        </view>
        <view class="login-title">登录后体验更多服务</view>
        <loginForm v-if="data.show" @change="data.show = false"></loginForm>
      </view>
    </wd-popup>
  </view>
</template>
<script>
export default {
  // 开启样式穿透
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script setup>
import { onShow } from '@dcloudio/uni-app'
import loginForm from './loginForm.vue'
import { reactive, watch, nextTick } from 'vue'
import { useUser } from '/src/utils/hook/useUser.js'
const { loginFun } = useUser()
const props = defineProps({
  verify: {
    type: Boolean,
    default: false,
  },
})

const data = reactive({
  show: false,
  loading: false,
  openid: '',
})
watch(
  () => data.show,
  () => {
    if (data.show) {
      uni.hideTabBar({
        fail: function () {
          console.log('失败')
        },
      })
    } else {
      uni.showTabBar({
        fail: function () {
          console.log('失败')
        },
      })
    }
  },
)
onShow(async () => {
  await nextTick
  loginFun.value = verifyLogin
  if (props.verify) {
    verifyLogin()
  }
})

/**
 * 验证登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 14:11:09
 * @param
 * @return
 */
const verifyLogin = () => {
  const token = uni.getStorageSync('tm_token')
  if (token) {
    return true
  } else {
    data.show = true
    return false
  }
}

defineExpose({ verifyLogin })
</script>

<style lang="scss" scoped>
.login-model {
  padding: 20rpx 40rpx;
  .top-logo {
    align-items: center;
    color: #606266;
    font-weight: bold;

    .logo-img {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
    }
  }
  .login-title {
    padding: 60rpx 0 0 0;
    font-size: 38rpx;
    font-weight: bold;
  }
}
</style>
