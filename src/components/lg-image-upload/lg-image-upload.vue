<template>
	<wd-upload
		:action="lgImageUpload.action"
		:header="lgImageUpload.headers"
		:formData="parameter"
		:limit="limit"
		:max-size="size * 1024 * 1024"
		:file-list="data.fileList"
		@success="onSuccess"
		@progress="emit('progress', e)"
		:useDefaultSlot="useDefaultSlot"
		@remove="onRemove"
	>
		<template #default v-if="$slots.default">
			<slot name="default"></slot>
		</template>
	</wd-upload>
</template>
<script>
export default {
	name: 'lgImageUpload'
};
</script>
<script setup>
import { inject, computed, reactive, watch, onMounted } from 'vue';
const props = defineProps({
	useDefaultSlot: {
		type: Boolean,
		default: false
	},
	modelValue: {
		type: [Array, String]
	},
	param: {
		type: Object,
		default() {
			return {};
		}
	},
	mode: {
		type: String,
		default: 'String'
	},
	limit: {
		type: Number,
		default: 1
	},
	size: {
		type: Number,
		default: 10
	},
	accept: {
		type: Array,
		default() {
			return [];
		}
	}
});
const data = reactive({
	fileList: [],
	flag: true
});
// 注入信息
const lgImageUpload = inject('lgImageUpload', () => {
	return {};
})();
const parameter = computed(() => {
	if (lgImageUpload.data) {
		return { ...lgImageUpload.data, ...props.param };
	}
	return props.param;
});

// 监听数据
watch(
	() => props.modelValue,
	() => {
		if (data.flag) {
			console.log('动态更新触发', props.modelValue);
			initData();
			data.flag = false;
		}
		data.flag = true;
	}
);
watch(
	() => data.fileList,
	() => {
		data.flag = false;
		formatList();
	}
);
const emit = defineEmits(['update:modelValue', 'change', 'progress']);
/**
 * 初始化图片
 * <AUTHOR> 张学勇
 * @date: 2023-11-23 17:49:10
 * @param
 * @return
 */
const initData = () => {
	if (props.mode == 'Array') {
		data.fileList = [];
		if (props.modelValue && props.modelValue.length) {
			props.modelValue.map((e) => {
				if (e) {
					data.fileList = [
						...data.fileList,
						...[
							{
								url: e,
								status: 'success'
							}
						]
					];
				}
			});
		}
	} else if (props.mode == 'String') {
		if (!props.modelValue && data.fileList.length) return (data.fileList = []);
		if (props.modelValue && props.modelValue.includes(';base64')) {
			data.fileList = [
				{
					url: props.modelValue,
					status: 'success'
				}
			];
		} else {
			// 忽略空串影响
			const list = props.modelValue ? props.modelValue.split(',') : [];
			list.map((e) => {
				data.fileList = [
					...data.fileList,
					...[
						{
							url: e,
							status: 'success'
						}
					]
				];
			});
		}
	}
};
/**
 * 格式化图片
 * <AUTHOR> 张学勇
 * @date: 2023-11-23 17:51:19
 * @param
 * @return
 */
const formatList = () => {
	data.flag = false;
	let list = null;
	if (props.mode == 'Array') {
		list = [];
		data.fileList.map((res) => {
			if (res.status == 'success') {
				if (res.response && res.response.data) {
					list = [...list, res.response.data];
				} else {
					list = [...list, res.url];
				}
			}
		});
	} else if (props.mode == 'String') {
		list = '';
		data.fileList.map((res, index) => {
			if (res.status == 'success') {
				if (res.response && res.response.data) {
					list = list + res.response.data + (data.fileList.length - 1 > index ? ',' : '');
				} else {
					list = list + res.url + (data.fileList.length - 1 > index ? ',' : '');
				}
			}
		});
	}
	emit('update:modelValue', list);
};

/**
 * 图片上传后的回调
 * <AUTHOR> 张学勇
 * @date: 2023-11-23 17:40:59
 * @param
 * @return
 */
const onSuccess = (e) => {
	if (e.file.response) {
		if (typeof e.file.response == 'string') {
			e.file.response = JSON.parse(e.file.response);
		}
		if (e.file.response.code == '200') {
			uni.showToast({
				icon: 'none',
				title: '上传成功',
				duration: 2000
			});
			data.fileList = [...data.fileList, e.file];
			emit('change', e.file, data.fileList);
			return;
		}
	}
	data.fileList = [...data.fileList, { ...e.file, status: 'fail' }];
};
/**
 * 图片移除后的回调
 * <AUTHOR> 张学勇
 * @date: 2023-11-24 16:42:50
 * @param
 * @return
 */
const onRemove = (e) => {
	console.log(e);
	data.fileList = data.fileList.filter((res) => {
		if (res.uid && e.status == 'success') {
			return res.uid != e.file.uid;
		} else {
			return res.url != e.file.url;
		}
	});
};

onMounted(() => {
	if (data.flag) {
		initData();
	}
});
</script>
