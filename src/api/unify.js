// 用户
import { request } from '/src/utils/request/index.js';
// 登录
export async function getLogin(payload) {
	return await request('/applet/user/login', { data: payload });
}
// 账号登录
export async function getAccountLogin(payload) {
	return await request('/applet/user/getAccountLogin', { data: payload });
}
// 获取短信
export async function getTelephoneCode(payload) {
	return await request('/applet/user/getTelephoneCode', { data: payload });
}
// 通过短信登陆
export async function getLoginCode(payload) {
	return await request('/applet/user/getLoginCode', { data: payload });
}
// 获取用户信息
export async function getUserInfo(payload = {}) {
	return await request('/applet/user/getUserinfo', { data: payload });
}
export async function getUserCode(payload = {}) {
	return await request('/applet/user/getUserCode', { data: payload });
}

// 公共配置
export async function getPlatformDeployList(payload = {}) {
	return await request('/applet/tourist/platformDeploy/getList', { data: payload });
}

// 领取活动优惠券
export async function setActivity(payload) {
	return await request('/applet/integralTask/activityReward/setActivity', { data: payload });
}
// 添加分享
export async function setShare(payload) {
	return await request('/applet/tourist/integralTask/setShare', { data: payload });
}
