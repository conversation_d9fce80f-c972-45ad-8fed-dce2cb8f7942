<template>
  <view class="min-h-screen bg-[#f3f3f3]">
    <!-- 顶部轮播图区域 -->
    <view class="relative">
      <at-swiper position="3" :height="240" />
    </view>
    <good-type />
    <view class="mt-2.5 bg-white p-4">
      <view class="flex justify-center items-center">
        <text class="text-48rpx font-500 text-#f4ab2f">好吃零食</text>
        <text class="text-48rpx font-500 text-#23222f">·等你选</text>
      </view>
      <view class="flex justify-center items-center py-1">
        <text class="text-24rpx text-gray-400">不负好食光</text>
      </view>
    </view>
    <view class="card-swiper">
      <at-swiper
        position="4"
        :height="240"
        :display-multiple-items="1"
        custom-indicator-class="custom-indicator-class"
        custom-image-class="custom-image"
        custom-next-image-class="custom-image-prev"
        custom-prev-image-class="custom-image-prev"
        previousMargin="24px"
        nextMargin="24px"
        customIndicatorClass="hidden!"
      />
    </view>
  </view>
</template>

<script setup>
import goodType from './components/good-type.vue'
</script>

<style lang="scss" scoped>
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  --wot-swiper-nav-dot-color: #e7e7e7;
  --wot-swiper-nav-dot-active-color: #4d80f0;
  background-color: #fff;
  padding-bottom: 24rpx;
  :deep(.custom-indicator-class) {
    bottom: -16px;
  }
  :deep(.custom-image) {
    border-radius: 12rpx;
  }
  :deep(.custom-image-prev) {
    height: 168px !important;
  }
}
</style>
