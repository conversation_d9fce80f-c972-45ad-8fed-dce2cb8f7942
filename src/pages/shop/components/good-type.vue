<template>
  <view class="page-grid" v-if="lists.length">
    <swiper
      :style="{ height: initClass() }"
      :indicator-dots="lists.length > 1"
      indicator-active-color="#1285FF"
    >
      <swiper-item v-for="(items, index) in lists" :key="index">
        <wd-grid :column="4">
          <wd-grid-item use-slot v-for="(item, index) in items" :key="item.id">
            <view @click="toGoodsList(item)">
              <view>
                <image :src="item.icon" class="item-imge"></image>
              </view>
              <view class="item-title">{{ item.name }}</view>
            </view>
          </wd-grid-item>
        </wd-grid>
      </swiper-item>
    </swiper>
  </view>
</template>
<script>
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script setup>
import { onMounted, ref } from 'vue'
import { getGoodsTypeList } from '../api/index.js'
import { onShow, onPullDownRefresh } from '@dcloudio/uni-app'
onPullDownRefresh(() => {
  initData()
})
const toGoodsList = (item) => {
  uni.navigateTo({
    url: `/pagesShop/pages/shop-type/index?id=${item.id}`,
  })
}
const lists = ref([])
const initData = () => {
  getGoodsTypeList({ is_home: '1' }).then((res) => {
    lists.value = chunkArray([].concat(res.data || []), 8)
  })
}
const initClass = () => {
  if (lists.value && lists.value.length) {
    if (lists.value.length > 1) {
      return '400rpx'
    } else if (lists.value[0].length > 4) {
      return '380rpx'
    }
  }
  return '180rpx'
}
onShow(() => {
  initData()
})
function chunkArray(array, chunkSize) {
  const result = []
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize)
    result.push(chunk)
  }
  return result
}
</script>
<style lang="scss" scoped>
.page-grid {
  padding: 0 32rpx;
  background-color: #fff;
  margin-top: 20rpx;

  .item-imge {
    height: 80rpx;
    width: 80rpx;
    border-radius: 16rpx;
  }

  .item-title {
    padding-top: 10rpx;

    color: #333333;
  }
}
</style>
