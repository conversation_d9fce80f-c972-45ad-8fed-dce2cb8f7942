<template>
	<view class="page-body">
		<view class="page-background-body"></view>
		<view class="page-body-position">
			<view class="lg-flea state-body">
				<image
					src="@/static/temporary-pay/success.png"
					class="card-image"
					v-if="state == '1'"
				></image>
				<image src="@/static/temporary-pay/fail.png" class="card-image" v-else></image>
				<view class="state-title">{{ msg }}</view>
			</view>

			<login-model></login-model>
			<view style="display: flex; justify-content: center">
				<view class="button-calss" @click="openPage">
					{{ state == '1' ? '完成' : '返回' }}
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
const msg = ref('未知错误')
const state = ref('2')
const type = ref('')
onLoad((e) => {
	msg.value = e.msg
	state.value = e.state
	if (e.type) {
		type.value = e.type
	}
})
const openPage = (e) => {
	if (state.value == '1') {
		uni.switchTab({
			url: '/pages/home/<USER>',
		})
	} else {
		uni.navigateBack()
	}
}
</script>

<style scoped lang="scss">
.time-text {
	padding-top: 40rpx;
	color: #ccc;
	font-size: 28rpx;
}
.page-body {
	position: relative;
	background-color: transparent;
}
.page-body-position {
	position: relative;
	z-index: 1;
	padding-bottom: 30rpx;
}
.page-background-body {
	position: absolute;
	width: 100%;
	height: 600rpx;
	background: linear-gradient(180deg, #e7f0fb, #fafafa);
}
.card-image {
	width: 128rpx;
	height: 128rpx;
}
.state-title {
	padding: 0 20rpx;
	padding-top: 30rpx;
}
.state-body {
	align-items: center;
	justify-content: center;
	padding: 60rpx 30rpx;
}
.button-calss {
	margin-top: 60rpx;
	padding: 26rpx 100rpx;
	background: #f1f1f1;
	border-radius: 16rpx;
	width: fit-content;
	font-weight: 500;
	font-size: 30rpx;
	color: #333333;
}
</style>
