<template>
	<view>
		<view class="item">
			<input class="input" v-model.trim="form_data.telephone" type="text" placeholder="请输入帐号" maxlength="20" />
		</view>
		<view class="item">
			<input class="input" v-model.trim="form_data.password" type="password" placeholder="请输入密码" maxlength="20" />
		</view>
		
		<view style="padding: 30rpx">
			<wd-button  style="width: 100%" size="large"
			 @click="submit"
			 :round="false">提交</wd-button>
		</view>
	</view>
</template>
<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useForm } from '/src/utils/form/useForm.js';
import { getAccountLogin } from '/src/api/unify.js';
import { useUser } from '/src/utils/hook/useUser.js';
const { initUserInfo } = useUser();

// 校验方法
const { validate } = useForm();

// 登录测试账号
const form_data = ref({
	telephone: '',
	password: ''
});
// 表单规则
const rule = {
	telephone: { type: 'string', required: true, message: '请输入帐号' },
	password: { type: 'string', required: true, message: '请输入密码' }
};
// 登录
async function submit() {
	const data = JSON.parse(JSON.stringify(form_data.value));
	// 校验
	validate(data, rule).then((res) => {
		getAccountLogin(data).then((res) => {
			uni.setStorageSync('tm_token', res.data.token);
			toast('登录成功');
			initUserInfo();
			uni.reLaunch({
			    url: '/pages/index/index'
			});
		});
	});
}
const toast = (e) => {
	uni.showToast({
		icon: 'none',
		title: e,
		duration: 2000
	});
};
</script>
<style lang="scss" scoped>
.item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 8rpx 40rpx;
	box-sizing: border-box;
	width: 680rpx;
	height: 98rpx;
	margin: 24rpx auto 0;
	background: #ffffff;
	border-radius: 16rpx;
	.input {
		flex: 1;
		height: 100%;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 28rpx;
	}
}
.code {
	padding-right: 8rpx;
}
</style>
