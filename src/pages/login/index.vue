<template>
	<view class="page-body">
		<view class="page-body-position">
			<view class="page-background-body">
				<view style="position: absolute; z-index: 11; bottom: 80rpx; width: 100%; display: flex; justify-content: center">
					<image src="/static/logo.jpg" class="logo-img" mode="widthFix"></image>
				</view>
				<image style="width: 100%; height: 100%" src="/static/logobg.png"></image>
			</view>
			<view class="page-card-top">欢迎使用</view>

			<view class="page-card-list">
				<view class="item">
					<wd-icon name="mobile" color="#808080" size="22px"></wd-icon>
					<input class="input" v-model.trim="form_data.telephone" type="text" placeholder="请输入手机号" maxlength="20" />
				</view>
				<view class="item">
					<wd-icon color="#808080" name="lock-on" size="22px"></wd-icon>
					<input class="input" v-model.trim="form_data.code" placeholder="请输入验证码" maxlength="20" />
					<view style="color: #1285ff" @click="getCode">
						{{ tips }}
					</view>
				</view>
				<view style="padding: 30rpx; padding-top: 60rpx">
					<wd-button style="width: 100%" size="large" @click="submit" :loading="datas.loading" :round="false">登录</wd-button>
				</view>

				<view style="padding: 30rpx; padding-top: 40rpx">
					<wd-button
						type="success"
						block
						size="large"
						:round="false"
						:open-type="authorize && !datas.disabled ? 'getPhoneNumber' : ''"
						@click="judgePhoneNumber"
						:loading="datas.wxloading"
						@getphonenumber="getPhoneNumber"
					>
						手机号快速登录
					</wd-button>
				</view>
			</view>
		</view>

		<u-verification-code ref="verificationCodeRef" @end="onend" @change="codeChange"></u-verification-code>
		<view class="page-buttom-card">
			<view class="buttom-agreement lg-flex">
				<wd-checkbox v-model="authorize" inline size="large" checked-color="#1285FF"></wd-checkbox>
				同意
				<text style="color: #1285ff" @click="onAgreement('employ')">《用户使用协议》</text>
				和
				<text style="color: #1285ff" @click="onAgreement('privacy')">《隐私保护协议》</text>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	options: {
		styleIsolation: 'shared'
	}
};
</script>
<script setup>
import { onLoad, onPageScroll } from '@dcloudio/uni-app';
import uVerificationCode from '/src/components/u-verification-code.vue';
import { useForm } from '/src/utils/form/useForm.js';
import { getTelephoneCode, getLoginCode, getLogin } from '/src/api/unify.js';
import { useUser } from '/src/utils/hook/useUser.js';
import { reactive, ref, onMounted } from 'vue';
import { throttle } from '/src/utils/utils.js';
const styleList = ref({});
const title = ref('');
const showPassword = ref(true);
const { initUserInfo, isLogin } = useUser();
const authorize = ref(true);
// 校验方法
const { validate } = useForm();
const tips = ref('获取验证码');
const verificationCodeRef = ref(null);
const datas = reactive({
	authorize: true,
	disabled: false,
	loading: false,
	wxloading: false,
	openid: ''
});
// 登录测试账号
const form_data = ref({
	telephone: '',
	code: ''
});
// 表单规则
const rule = {
	telephone: [['required', '请输入手机号'], 'telephone'],
	code: { type: 'string', required: true, message: '请输入验证码' }
};
const getCode = () => {
	throttle(() => {
		if (verificationCodeRef.value.canGetCode) {
			validate({ telephone: form_data.value.telephone }, { telephone: [['required', '请输入手机号'], 'telephone'] }).then((res) => {
				getTelephoneCode({ telephone: form_data.value.telephone }).then((res) => {
					toast(res.msg);
					verificationCodeRef.value.start();
				});
			});
		} else {
			toast('倒计时结束后再发送');
		}
	});
};

// 倒计时结束
function onend(e) {
	tips.value = '重新获取';
}
// 倒计时
function codeChange(text) {
	tips.value = text;
}
// 登录
async function submit() {
	if (!authorize.value) {
		toast('请阅读并勾选用户协议');
		return;
	}

	const data = JSON.parse(JSON.stringify(form_data.value));
	// 校验
	validate(data, rule).then((res) => {
		// #ifdef  MP-WEIXIN
		wx.login({
			success: function (res) {
				datas.loading = true;
				getLoginCode({ ...data, wxCode: res.code })
					.then((res) => {
						uni.setStorageSync('tm_token', res.data.token);
						initUserInfo(false, () => {
							toast('登录成功');
							setTimeout(() => {
								uni.navigateBack({
									fail: function () {
										uni.reLaunch({
											url: '/pages/index/index'
										});
									}
								});
							}, 500);
						});
					})
					.finally(() => {
						datas.loading = false;
					});
				// setLogin({
				// 	codePhone: e.code,
				// 	code: res.code
				// });
			},
			fails: function () {
				toast('获取用户消息失败,无法登录');
			}
		});
		//#endif
	});
}
const toast = (e) => {
	uni.showToast({
		icon: 'none',
		title: e,
		duration: 2000
	});
};

onMounted(() => {
	judgeLogin();
});

/**
 * 触发微信手机号登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 15:42:27
 * @param
 * @return
 */
const getPhoneNumber = (e) => {
	if (e.code) {
		// #ifdef  MP-WEIXIN
		wx.login({
			success: function (res) {
				setLogin({
					codePhone: e.code,
					code: res.code
				});
			},
			fails: function () {
				toast('获取用户消息失败,无法登录');
			}
		});
		//#endif
	} else {
		toast('无法获取手机号码');
	}
};

/**
 * 处理登录逻辑
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 15:42:02
 * @param
 * @return
 */
const setLogin = (e) => {
	datas.wxloading = true;
	getLogin(e)
		.then((res) => {
			uni.setStorageSync('tm_token', res.data.token);
			initUserInfo(false, () => {
				toast('登录成功');
				setTimeout(() => {
				uni.navigateBack({
					fail: function () {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}
				});
				}, 500);
			});
		})
		.finally(() => {
			datas.wxloading = false;
		});
};

/**
 * 判断是否恶意进行手机号登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 15:51:13
 * @param
 * @return
 */
const judgePhoneNumber = () => {
	if (!authorize.value) {
		toast('请阅读并勾选用户协议');
	} else {
		const click_data = uni.getStorageSync('click_login');
		if (click_data && click_data.sum > 40) {
			datas.disabled = true;
			toast('请求过于频繁，请1小时后重试');
		} else if (click_data && click_data.sum > 39) {
			uni.setStorageSync('click_login', {
				date: Date.parse(new Date()),
				sum: click_data.sum + 1
			});
			datas.disabled = true;
		} else {
			uni.setStorageSync('click_login', {
				date: Date.parse(new Date()),
				sum: click_data.sum + 1
			});
		}
	}
};

/**
 * 检验是否可以使用手机号登录
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 16:19:04
 * @param
 * @return
 */
const judgeLogin = () => {
	const click_data = uni.getStorageSync('click_login');
	if (click_data && click_data.sum > 40) {
		if (timeFrom(click_data.date)) {
			uni.setStorageSync('click_login', {
				date: Date.parse(new Date()),
				sum: 1
			});
		} else {
			datas.disabled = true;
		}
	}
};

// 查看协议
const onAgreement = (e) => {
	if (!datas.loading) {
		uni.navigateTo({
			url: `/pages/my/system-manage/protocol-details/index?name=${e}`
		});
	}
};
</script>
<style>
page {
	background-color: #fff;
}
</style>
<style scoped lang="scss">
:deep(.wd-button) {
	width: 100%;
}
:deep(.wd-button.is-success) {
	background: #1bc15c !important;
}
.logo-img {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
}
.page-buttom-card {
	width: 100%;
	position: fixed;
	bottom: 0;
	z-index: 1;
	background-color: #fff;
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	margin-bottom: 30rpx;
	/* #ifdef H5 */
	bottom: 90rpx;
	/* #endif */
	.buttom-agreement {
		padding: 30rpx 0 0 0;
		text-align: center;
		font-size: 26rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #909399;
	}
}
.item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 8rpx 40rpx;
	box-sizing: border-box;
	width: 680rpx;
	height: 98rpx;
	margin: 24rpx auto 0;
	background: #fafafa;
	border-radius: 16rpx;
	.input {
		flex: 1;
		height: 100%;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 28rpx;
	}
}
.code {
	padding-right: 8rpx;
}
:deep(.wd-button) {
	width: 100%;
	background: #0f9dff;
}
.item-class {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx 30rpx 0 30rpx;
	padding: 20px;
	background-color: #fff;
	border-radius: 16rpx;
}
.page-card-top {
	padding: 20rpx;
	display: flex;
	justify-content: center;
	font-weight: 400;
	font-size: 42rpx;
	color: #333333;
}
.page-card-list {
}

.page-body {
	position: relative;
	background-color: #fff;
}
.page-body-position {
	position: absolute;
	width: 100%;
	top: 0;
	z-index: 1;
	// margin-top: 400rpx;
	background-color: #fff;
}
.page-background-body {
	position: relative;
	width: 100%;
	height: 400rpx;
	background: #2d8ef9;
}
</style>
