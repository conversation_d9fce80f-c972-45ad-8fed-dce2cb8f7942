<template>
  <view class="lg-flea" style="padding: 30rpx 0">
    <form-item label="个人头像" isRequired layout="line">
      <button class="avatar" open-type="chooseAvatar" @chooseavatar="changeAvatar">
        <image v-if="form.avatar" class="img" :src="form.avatar" mode="aspectFill"></image>
        <image v-else src="@/static/avatar.jpg" class="img"></image>
      </button>
    </form-item>
    <form-item label="昵称" isRequired layout="line">
      <input
        type="nickname"
        v-model="form.nickname"
        placeholder="请输入"
        class="nickname-input"
        @change="oninput"
      />
    </form-item>
    <!-- 	<form-item label="手机号码" isRequired layout="line">
			<wd-input no-border v-model="form.nickname" class="rgint-input" placeholder="请输入" />
		</form-item> -->
    <form-item label="生日" layout="line">
      <lg-datetime-picker maxDate="2024-12-18" minDate="1910-01-01" v-model="form.birthday" />
    </form-item>
    <form-item label="性别" isRequired layout="line">
      <wd-picker v-model="form.sex" :columns="sexList" align-right />
    </form-item>
    <!-- <form-item label="民族" layout="line">
				<wd-picker v-model="form.extend.nation" :columns="chineseEthnicGroups" align-right />
		</form-item>
		<form-item label="驾龄" layout="line">
			<wd-input type="number " no-border v-model="form.extend.driving" class="rgint-input" placeholder="请输入" use-suffix-slot>
				<template #suffix>
					<text>年</text>
				</template>
			</wd-input>
		</form-item> -->
    <form-item label="地区" layout="line">
      <view @click="onAddress" class="text-rgiht">
        <view class="address" v-if="form.extend.region">{{ form.extend.region }}</view>
        <view class="address adds-placeholder" v-else>请选择省/市/区/街道</view>
        <wd-icon name="arrow-right" size="12px" color="#999999"></wd-icon>
      </view>
    </form-item>
    <view style="padding: 30rpx">
      <wd-button style="width: 100%" size="large" @click="onSubmit()" :round="false">
        确定
      </wd-button>
    </view>
  </view>
</template>

<script>
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script setup>
import { ref } from 'vue'
import { throttle } from '/src/utils/utils.js'
import { onLoad, onShow } from '@dcloudio/uni-app'
import formItem from './components/form-item.vue'
import lgDatetimePicker from '/src/components/lg-datetime-picker/index.vue'
import { useUser } from '/src/utils/hook/useUser.js'
import { setUserUpdate, setUpload } from './api/index.js'
import { useForm } from '/src/utils/form/useForm.js'
const { validate } = useForm()
const { initUserInfo } = useUser()
import { useState } from '../hooks/index.js'
const { initData, datas } = useState()
const form = ref({ extend: {} })
const sexList = [
  { value: '0', label: '男' },
  { value: '1', label: '女' },
]
const chineseEthnicGroups = [
  { value: '汉族', label: '汉族' },
  { value: '蒙古族', label: '蒙古族' },
  { value: '回族', label: '回族' },
  { value: '藏族', label: '藏族' },
  { value: '维吾尔族', label: '维吾尔族' },
  { value: '苗族', label: '苗族' },
  { value: '彝族', label: '彝族' },
  { value: '壮族', label: '壮族' },
  { value: '布依族', label: '布依族' },
  { value: '朝鲜族', label: '朝鲜族' },
  { value: '满族', label: '满族' },
  { value: '侗族', label: '侗族' },
  { value: '瑶族', label: '瑶族' },
  { value: '白族', label: '白族' },
  { value: '土家族', label: '土家族' },
  { value: '哈尼族', label: '哈尼族' },
  { value: '哈萨克族', label: '哈萨克族' },
  { value: '傣族', label: '傣族' },
  { value: '黎族', label: '黎族' },
  { value: '傈僳族', label: '傈僳族' },
  { value: '佤族', label: '佤族' },
  { value: '畲族', label: '畲族' },
  { value: '高山族', label: '高山族' },
  { value: '拉祜族', label: '拉祜族' },
  { value: '水族', label: '水族' },
  { value: '东乡族', label: '东乡族' },
  { value: '纳西族', label: '纳西族' },
  { value: '景颇族', label: '景颇族' },
  { value: '柯尔克孜族', label: '柯尔克孜族' },
  { value: '土族', label: '土族' },
  { value: '达斡尔族', label: '达斡尔族' },
  { value: '仫佬族', label: '仫佬族' },
  { value: '羌族', label: '羌族' },
  { value: '布朗族', label: '布朗族' },
  { value: '撒拉族', label: '撒拉族' },
  { value: '毛南族', label: '毛南族' },
  { value: '仡佬族', label: '仡佬族' },
  { value: '锡伯族', label: '锡伯族' },
  { value: '阿昌族', label: '阿昌族' },
  { value: '普米族', label: '普米族' },
  { value: '塔吉克族', label: '塔吉克族' },
  { value: '怒族', label: '怒族' },
  { value: '乌孜别克族', label: '乌孜别克族' },
  { value: '俄罗斯族', label: '俄罗斯族' },
  { value: '鄂温克族', label: '鄂温克族' },
  { value: '德昂族', label: '德昂族' },
  { value: '保安族', label: '保安族' },
  { value: '裕固族', label: '裕固族' },
  { value: '京族', label: '京族' },
  { value: '塔塔尔族', label: '塔塔尔族' },
  { value: '独龙族', label: '独龙族' },
  { value: '鄂伦春族', label: '鄂伦春族' },
  { value: '赫哲族', label: '赫哲族' },
  { value: '门巴族', label: '门巴族' },
  { value: '珞巴族', label: '珞巴族' },
  { value: '基诺族', label: '基诺族' },
]
const oninput = (e) => {
  form.value.nickname = e.detail.value
}
const rules = {
  avatar: { type: 'string', required: true, message: '请上传头像' },
  nickname: { type: 'string', required: true, message: '请输入昵称' },
  sex: {
    type: 'string',
    validator: (rule, value, callback) => {
      if (value == '2') {
        return Promise.reject('请选择性别')
      } else {
        return Promise.resolve()
      }
    },
    required: true,
  },
}
onLoad(() => {
  form.value = JSON.parse(JSON.stringify(datas.value))
})
const onAddress = () => {
  uni.chooseLocation({
    success: (res) => {
      form.value.extend.region = res.address
    },
    fail: (err) => {
      console.log('chooseAddress error', err)
    },
  })
}
const onSubmit = () => {
  throttle(() => {
    const data = JSON.parse(JSON.stringify(form.value))
    validate(data, rules).then((res) => {
      uni.showLoading({ mask: true })
      setTimeout(function () {
        uni.hideLoading()
      }, 2000)
      setUserUpdate({ ...form.value })
        .then((res) => {
          uni.showToast({
            icon: 'none',
            title: '更新成功',
            duration: 2000,
          })
          initUserInfo()
          setTimeout(function () {
            uni.navigateBack()
          }, 500)
        })
        .finally(() => {
          uni.hideLoading()
        })
    })
  })
}
function changeAvatar(e) {
  uni.showLoading({ mask: true })
  setTimeout(function () {
    uni.hideLoading()
  }, 2000)
  setUpload(e.detail.avatarUrl)
    .then((res) => {
      form.value.avatar = res.data
    })
    .finally(() => {
      uni.hideLoading()
    })
}
</script>

<style scoped lang="scss">
::v-deep .placeholder-class {
  color: #bfbfbf;
  font-weight: 400;
  font-size: 20rpx;
}
.nickname-input {
  text-align: right;
  font-size: 28rpx;
}
:deep(.wd-textarea__inner) {
  height: 100rpx;
}
:deep(.wd-picker__cell) {
  padding: 0 !important;
}
:deep(.wd-button) {
  width: 100%;
}
:deep(.wd-input__body) {
  text-align: right;
}
.text-rgiht {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
  .address {
    text-align: right;
    width: 500rpx;
    padding-right: 20rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 24rpx;
  }
  .adds-placeholder {
    color: #bfbfbf;
  }
}
.avatar {
  border: none;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: transparent;
  &::after {
    border: none;
  }
  .img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin: 0;
  }
}
</style>
