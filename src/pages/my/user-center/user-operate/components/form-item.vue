<template>
	<view class="form-item-card">
		<view class="item-card-body" :class="[layout=='vertical'?'vertical-class':'line-class']">
			<view class="title" v-if="label">
				<text v-if="isRequired">*</text>
				<text class="label">{{ label }}</text>
			</view>
			<view class="content-body">
				<slot></slot>
			</view>
			
		</view>
	</view>
</template>

<script setup>
const props = defineProps({
	label: {
		type: String,
		default: ''
	},
	isRequired: {
		type: Boolean,
		default: false
	},
	layout:{
		type: String,
		default: 'vertical'
	}
});
</script>
<style lang="scss" scoped>
.form-item-card {
	padding: 15rpx 30rpx;
	.item-card-body{
		background: #fff;
		border-radius: 16rpx;
		padding: 40rpx 30rpx;
		box-sizing: border-box;
	}
	.vertical-class{
		display: flex;
		flex-direction: column;
		.title {
			font-weight: bold;
			padding-bottom: 40rpx;
			color: #1285ff;
			.label {
				color: #000;
			}
		}
	}
	.line-class{
		display: flex;
		align-items: center;
		.title {
			padding-right: 20rpx;
			font-weight: bold;
			color: #1285ff;
			.label {
				color: #000;
			}
		}
		.content-body{
			flex: 1;
			
		}
	}
	
}
</style>
