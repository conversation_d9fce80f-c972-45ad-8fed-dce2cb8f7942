<template>
  <view class="lg-flea">
    <view class="item-class">
      <view class="label-class">个人头像</view>
      <view class="lg-flex center-item">
        <view>
          <image v-if="datas.avatar" :src="datas.avatar" class="avatar-class"></image>
          <image v-else src="@/static/avatar.jpg" class="avatar-class"></image>
        </view>
      </view>
    </view>
    <view class="item-class">
      <view class="label-class">昵称</view>
      <view class="lg-flex center-item">
        <view>{{ initText(datas.nickname) }}</view>
      </view>
    </view>
    <view class="item-class">
      <view class="label-class">手机号码</view>
      <view class="lg-flex center-item">
        <view>{{ initText(datas.telephone) }}</view>
      </view>
    </view>

    <view class="item-class">
      <view class="label-class">生日</view>
      <view class="lg-flex center-item">
        <view>{{ initText(datas.birthday) }}</view>
      </view>
    </view>
    <view class="item-class">
      <view class="label-class">性别</view>
      <view class="lg-flex center-item">
        <view>{{ initText(sexType[datas.sex]) }}</view>
      </view>
    </view>
    <!-- <view class="item-class">
            <view class="label-class">民族</view>
            <view class="lg-flex center-item">
                <view>{{initText(datas.extend?.nation) }}</view>
            </view>
        </view>
        <view class="item-class">
            <view class="label-class">驾龄</view>
            <view class="lg-flex center-item">
                <view>{{initText(datas.extend?.driving) }}年</view>
            </view>
        </view> -->
    <view class="item-class">
      <view class="label-class">地区</view>
      <view class="lg-flex center-item">
        <view>{{ initText(datas.extend?.region) }}</view>
      </view>
    </view>

    <view style="padding: 30rpx">
      <wd-button
        v-if="datas.id"
        @click="openPage()"
        style="width: 100%"
        size="large"
        :round="false"
      >
        编辑信息
      </wd-button>
    </view>
  </view>
</template>
<script>
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script setup>
import { useState } from './hooks/index.js'
import { onShow } from '@dcloudio/uni-app'
const { initData, datas } = useState()
onShow(() => {
  initData()
})
const openPage = () => {
  uni.navigateTo({
    url: `/pages/my/user-center/user-operate/index`,
  })
}
const sexType = {
  0: '男',
  1: '女',
  2: '未知',
}

const initText = (e) => {
  if (e || e === 0) {
    return e
  } else {
    return '-'
  }
}
</script>

<style lang="scss" scoped>
:deep(.wd-button) {
  width: 100%;
}
.center-item {
  align-items: center;
  flex-wrap: wrap;
}
.label-class {
  flex-shrink: 0;
  padding-right: 20rpx;
}
.avatar-class {
  height: 80rpx;
  width: 80rpx;
  border-radius: 50%;
}
.item-class {
  display: flex;
  justify-content: space-between;
  line-height: 40rpx;
  margin: 30rpx 30rpx 0 30rpx;
  padding: 20px;
  background-color: #fff;
  border-radius: 16rpx;
}
</style>
