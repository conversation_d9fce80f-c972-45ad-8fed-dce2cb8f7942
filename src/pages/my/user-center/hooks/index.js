import { ref } from 'vue';
import { getInfo } from '../api/index.js';
const datas = ref({sex:'2',extend:{}});
export function useState() {
    const initData = async () => {
        getInfo().then(async (res) => {
            datas.value = res.data;
            if (!res.data.extend) {
                datas.value['extend'] = {};
            }
        });
    };
    return {
        initData,
        datas
    };
}
