<template>
    <view class="lg-flea">
        <view class="item-class" @click="openPage('privacy')">
            <text>用户隐私政策</text>
            <wd-icon name="chevron-right" size="22px"></wd-icon>
        </view>
        <view class="item-class" @click="openPage('employ')">
            <text>用户使用协议</text>
            <wd-icon name="chevron-right" size="22px"></wd-icon>
        </view>

        <view style="padding: 30rpx" v-if="isLogin">
            <wd-button style="width: 100%"  @click="onQuit" size="large" :round="false">退出登录</wd-button>
        </view>
    </view>
</template>
<script>
export default {
    options: {
        styleIsolation: 'shared'
    }
};
</script>
<script setup>
import { useUser } from '/src/utils/hook/useUser.js';
const { logout, isLogin } = useUser();
const onQuit = () => {
    logout();
};
const openPage = (e) => {
    uni.navigateTo({
        url: `/pages/my/system-manage/protocol-details/index?name=${e}`
    });
};
</script>

<style lang="scss" scoped>
:deep(.wd-button) {
    width: 100%;
}
.item-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30rpx 30rpx 0 30rpx;
    padding: 20px;
    background-color: #fff;
    border-radius: 16rpx;
}
</style>
