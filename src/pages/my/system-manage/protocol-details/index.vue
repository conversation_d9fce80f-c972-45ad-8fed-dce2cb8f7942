<template>
	<view class="protocol-body">
		<uParse :content="datas.content"></uParse>
	</view>
</template>

<script setup>

import { onLoad } from '@dcloudio/uni-app';
import uParse from '/src/components/u-parse/u-parse.vue';
import { getDetail } from './api/index.js';
import { ref } from 'vue';
const datas = ref({});
onLoad((e) => {
	
	
	if(e.name=='employ'){
		uni.setNavigationBarTitle({
		  title: '用户协议'
		});
		initData(e.name)
	}else{
		uni.setNavigationBarTitle({
		  title: '隐私协议'
		});
		initData(e.name)
	}
})
const initData = (e) => {
	getDetail({ name: e }).then((res) => {
		datas.value = res.data;
	});
};

</script>

<style>
page {
	background-color: #fff;
}
</style>
<style lang="scss" scoped>
.protocol-body {
	padding: 40rpx 30rpx;
}
</style>