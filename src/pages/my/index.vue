<template>
  <view class="page-body">
    <view class="page-background-body"></view>
    <view class="page-body-position">
      <page-user></page-user>
      <page-member-card></page-member-card>
      <!-- <wxadSwiper unit-id="adunit-cc469f8e5559aece" paddingTop="30rpx"></wxadSwiper> -->
      <page-list></page-list>
      <!-- <at-overlay-swiper position="5"></at-overlay-swiper> -->
      <view class="mx-4 mb-8">
        <view class="mb-3">
          <text class="text-base font-semibold text-#333333">VIP福利专区</text>
          <text class="text-xs text-#999999 ml-2">积分商城即将上线...</text>
        </view>
        <at-swiper :position="'5'" :height="95"></at-swiper>
      </view>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <login-model></login-model>
    <!-- #endif -->
  </view>
</template>

<script setup>
import pageUser from './components/page-user.vue'
import pageMemberCard from './components/page-member-card.vue'
import pageList from './components/page-list.vue'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import { ref } from 'vue'
const share_id = ref('')
import { onShare } from '/src/utils/open-menu.js'
let interstitialAd = null
onLoad((e) => {
  // // 在页面onLoad回调事件中创建插屏广告实例
  // if (wx.createInterstitialAd) {
  //   interstitialAd = wx.createInterstitialAd({
  //     adUnitId: 'adunit-c516b6a523f19a23',
  //   })
  //   interstitialAd.onLoad(() => {})
  //   interstitialAd.onError((err) => {
  //     console.error('插屏广告加载失败', err)
  //   })
  //   interstitialAd.onClose(() => {})
  // }
  // // 在适合的场景显示插屏广告
  // if (interstitialAd) {
  //   interstitialAd.show().catch((err) => {
  //     console.error('插屏广告显示失败', err)
  //   })
  // }
  // share_id.value = e.share_id
})
onShareAppMessage(() => {
  onShare(share_id.value)
  return {
    title: '',
    path: '/pages/index/index',
  }
})
</script>

<style scoped lang="scss">
.page-body {
  position: relative;
  background-color: transparent;
}
.page-body-position {
  position: relative;
  z-index: 1;
  padding-bottom: 30rpx;
}
.page-background-body {
  position: absolute;
  width: 100%;
  height: 600rpx;
  background: linear-gradient(180deg, #e7f0fb, #fafafa);
}
</style>
