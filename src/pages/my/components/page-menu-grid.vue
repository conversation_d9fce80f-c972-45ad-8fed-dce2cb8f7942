<template>
  <view class="page-grid">
    <wd-grid :column="4">
      <wd-grid-item use-slot v-for="(item, index) in list" :key="index">
        <view @click="openPage(item.url)">
          <view>
            <image :src="item.imge" class="item-imge"></image>
          </view>
          <view class="item-title">{{ item.name }}</view>
        </view>
      </wd-grid-item>
    </wd-grid>
  </view>
</template>
<script>
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script setup>
import { useUser } from '/src/utils/hook/useUser.js'
const { isLogin, setLogin } = useUser()
const list = [
  { name: '我的订单', url: '/pagesMy/pages/order-manage/index', imge: '/static/my/dingdan.png' },
  { name: '我的优惠券', url: '/pagesMy/pages/my-coupon/index', imge: '/static/my/youhuiquan.png' },
  { name: '我的月租卡', url: '/pagesD/pages/my-month-card/index', imge: '/static/my/yuezhuka.png' },
  { name: '开票记录', url: '/pagesD/pages/my-receipt/index', imge: '/static/my/kaipiao.png' },
]
const openPage = (e) => {
  // if (isLogin.value) {
  //   if (e) {
  uni.navigateTo({
    url: e,
  })
  //   } else {
  //     uni.showToast({
  //       icon: 'none',
  //       title: '开发中。。。',
  //       duration: 2000,
  //     })
  //   }
  // } else {
  //   setLogin()
  // }
}
</script>
<style lang="scss" scoped>
.page-grid {
  margin: 0 32rpx;
  padding: 0 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-top: 20rpx;
  .item-imge {
    height: 80rpx;
    width: 80rpx;
    border-radius: 16rpx;
  }

  .item-title {
    padding-top: 10rpx;

    color: #333333;
  }
}
</style>
