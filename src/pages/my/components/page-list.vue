<template>
  <view class="list-body">
    <view v-for="(item, index) in data.list" :key="index">
      <button v-if="item.url == 'feedback'" class="feedback-button" open-type="feedback">
        <view class="lg-flex item-lits">
          <view class="body-item">
            <image :src="item.icon" class="item-imge"></image>
            {{ item.name }}
          </view>
          <view><wd-icon name="arrow-right" size="22px"></wd-icon></view>
        </view>
      </button>
      <view class="lg-flex item-lits" @click="openPage(item.url, index)" v-else>
        <view class="body-item">
          <image :src="item.icon" class="item-imge"></image>
          {{ item.name }}
        </view>
        <view><wd-icon name="arrow-right" size="22px"></wd-icon></view>
      </view>
    </view>
    <view class="lg-flex item-lits" @click="openLogin()" v-if="is_test()">
      <view class="body-item">
        <image src="/static/my/taitou.png" class="item-imge"></image>
        测试登录
      </view>
      <view><wd-icon name="arrow-right" size="22px"></wd-icon></view>
    </view>
  </view>
</template>

<script setup>
import { reactive } from 'vue'
import { useUser } from '/src/utils/hook/useUser.js'
import { useDeploy } from '/src/utils/hook/useDeploy.js'
const { isLogin, setLogin } = useUser()
const { platformDeployObj } = useDeploy()
const is_test = () => {
  return import.meta.env.VITE_USER_NODE_ENV == 'development'
}
const openLogin = () => {
  uni.navigateTo({
    url: '/pages/login/test',
  })
}

const openPage = (e, index) => {
  if (e == 'contact') {
    if (platformDeployObj.value && platformDeployObj.value.contact) {
      try {
        uni.makePhoneCall({
          phoneNumber: platformDeployObj.value.contact.data.telephone,
        })
      } catch (error) {
        uni.showToast({
          icon: 'none',
          title: '暂未配置。。。',
          duration: 2000,
        })
      }
    } else {
      uni.showToast({
        icon: 'none',
        title: '正在开通中。。。',
        duration: 2000,
      })
    }

    return
  }
  if ([0, 1, 3, 5].some((res) => res === index) && !isLogin.value) {
    setLogin()
    return
  }
  if (e) {
    uni.navigateTo({
      url: e,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '开发中。。。',
      duration: 2000,
    })
  }
}

const data = reactive({
  list: [
    {
      icon: '/static/profile/order.png',
      name: '全部订单',
      url: '/pagesMy/pages/my-order/index',
    },
    {
      icon: '/static/profile/service.png',
      name: '专属客服',
      url: 'contact',
    },
    {
      icon: '/static/profile/feedback.png',
      name: '在线反馈',
      url: '/pagesD/pages/online-feedback/index',
    },
    {
      icon: '/static/profile/about.png',
      name: '关于我们',
      url: '/pagesD/pages/about-us/index',
    },
    {
      icon: '/static/profile/setting.png',
      name: '设置',
      url: '/pages/my/system-manage/index',
    },
  ],
})
</script>

<style lang="scss" scoped>
.feedback-button {
  border: none;
  width: 100%;
  font-size: 28rpx;
  background: transparent;
  padding: 0;
  &::after {
    border: none;
  }
}
.list-body {
  margin: 24rpx 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  .body-item {
    color: #333333;
    display: flex;
    align-items: center;
  }
  .item-imge {
    height: 42rpx;
    width: 42rpx;
    padding-right: 20rpx;
  }
  .item-lits {
    justify-content: space-between;
    align-items: center;
    height: 88rpx;
    padding: 0 44rpx;
  }
  .contact {
    position: relative;
    text-align: center;
    text-decoration: none;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    width: 100%;
    background: transparent;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    &::after {
      border: none;
    }
  }
}
</style>
