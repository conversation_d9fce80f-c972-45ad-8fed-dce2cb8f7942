<template>
  <view class="page-user-bg" v-if="isLogin">
    <wd-navbar
      title="个人中心"
      custom-style="background-color: transparent !important;--wot-navbar-color: #fff;"
      safeAreaInsetTop
      :bordered="false"
    ></wd-navbar>
    <view class="page-user text-#fff" @click="openPage">
      <image v-if="user.avatar" :src="user.avatar" class="user-avatar"></image>
      <image v-else src="@/static/avatar.jpg" class="user-avatar"></image>
      <view class="user-item">
        <view class="user-name">
          {{ user.nickname }}
          <wd-icon name="arrow-right" custom-class="ml-2" size="22px"></wd-icon>
          <!-- <image src="/static/my/xiugai.png" class="user-icon"></image> -->
        </view>
        <view class="user-telephone">
          <wd-icon name="mobile" size="22px"></wd-icon>
          {{ user.telephone }}
        </view>
      </view>
    </view>
    <view class="mx-4 p-3 bg-white rounded-[8rpx_8rpx_0_0] grid grid-cols-2 gap-20rpx">
      <view
        class="flex flex-col items-center justify-between"
        @click="openPage('/pagesMy/pages/my-wallet/index')"
      >
        <view class="text-#333 pb-2 text-36rpx font-500">123123</view>
        <view class="text-#333 text-28rpx">钱包余额</view>
      </view>
      <view
        class="flex flex-col items-center justify-between"
        @click="openPage('/pagesMy/pages/my-coupon/index')"
      >
        <view class="text-#333 pb-2 text-36rpx font-500">5张</view>
        <view class="text-#333 text-28rpx">我的优惠券</view>
      </view>
    </view>
  </view>
  <view v-else>
    <wd-navbar
      title="个人中心"
      custom-style="background-color: transparent !important;--wot-navbar-color: #fff;"
      safeAreaInsetTop
      :bordered="false"
    ></wd-navbar>
    <view class="page-user pt-safe" @click="setLogin">
      <view class="you-logo">游</view>
      <view class="user-item user-name">请登录~</view>
    </view>
  </view>
</template>

<script setup>
import { useUser } from '/src/utils/hook/useUser.js'
const { isLogin, setLogin, user } = useUser()
const openPage = (e) => {
  uni.navigateTo({
    url: e || '/pages/my/user-center/index',
  })
}
</script>

<style scoped lang="scss">
.page-user-bg {
  background-image: url('/static/profile/profile-bg.png');
  background-size: 100% 554rpx;
  background-repeat: no-repeat;
}
.page-user {
  padding: 40rpx;
  display: flex;
  align-items: center;
  .user-avatar {
    width: 128rpx;
    height: 128rpx;
    border-radius: 50%;
  }
  .user-item {
    flex: 1 1 0;
    padding: 0 40rpx;
  }
  .user-name {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 48rpx;
  }
  .user-icon {
    width: 40rpx;
    height: 40rpx;
    padding-left: 10rpx;
  }
  .user-telephone {
    font-weight: 400;
    font-size: 30rpx;
    padding-top: 20rpx;
  }
  .you-logo {
    font-weight: bold;
    width: 90rpx;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 38rpx;
    color: #ffffff;
    background: #1285ff;
    border-radius: 50%;
  }
}
</style>
