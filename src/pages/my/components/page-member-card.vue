<template>
  <view class="at-swiper" v-if="!lists.length">
    <view class="page-member-card" @click="openPage">
      <view>
        <view class="left-class">
          <image src="/src/static/temporary-pay/huiyan.png" class="icon-class"></image>
          会员专享
        </view>
        <view class="left-text">会员特惠，尽享尊贵~</view>
      </view>
      <view class="rgiht-button">开通会员</view>
    </view>
  </view>
  <view class="at-swiper" v-else>
    <swiper style="height: 160rpx" :indicator-dots="lists.length > 1" indicator-active-color="#fff">
      <swiper-item v-for="items in lists" :key="items.id">
        <view
          class="page-member-card"
          :style="{ backgroundColor: initDeploy(items.deploy_class).bgColor }"
        >
          <view>
            <view
              class="left-class line1"
              style="padding-left: 34rpx"
              :style="{ color: initDeploy(items.deploy_class).titleColor }"
            >
              <!-- <image src="/src/static/temporary-pay/huiyan.png" class="icon-class"></image> -->
              {{ items.title }}
            </view>
            <view
              class="left-text"
              :style="{ color: initDeploy(items.deploy_class).describeColor }"
              v-if="items.status == 4"
            >
              已过期
            </view>
            <view
              class="left-text"
              :style="{ color: initDeploy(items.deploy_class).describeColor }"
              v-else
            >
              {{ items.start_date }} 至 {{ items.end_date }}
            </view>
          </view>
          <view class="rgiht-button" v-if="items.status == 4" @click="openPage">去续费</view>
          <view v-else>
            <img
              :src="items.image"
              style="width: 80rpx; height: 80rpx; padding-right: 14rpx"
              v-if="items.image"
            />
            <image
              v-else
              src="/src/static/my/xinxin.png"
              style="width: 80rpx; height: 80rpx; padding-right: 14rpx"
            ></image>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { getMemberList } from '../api/index.js'
import { onShow } from '@dcloudio/uni-app'
const lists = ref([])

const initDeploy = (e) => {
  if (e) {
    return e
  } else {
    return {
      bgColor: '#3A3D52',
      titleColor: '#FEDFB2',
      describeColor: '#9C9FA5',
      moneyColor: '#FEDFB2',
    }
  }
}
const initData = () => {
  getMemberList({ is_user: true }).then((res) => {
    lists.value = res.data
  })
}
onShow(() => {
  // initData()
})
const openPage = (e) => {
  uni.switchTab({
    url: '/pages/member/index',
  })
}
const show = ref(false)
</script>

<style lang="scss" scoped>
.at-swiper {
  padding: 30rpx;
}
.page-member-card {
  background-color: #3a3d52;

  display: flex;
  align-items: center;
  padding: 30rpx;
  justify-content: space-between;
  border-radius: 16rpx;
  .left-class {
    font-weight: bold;
    font-size: 32rpx;
    color: #fedfb2;
    display: flex;
    align-items: center;
  }
  .left-text {
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
    padding-top: 20rpx;
    padding-left: 34rpx;
  }
  .rgiht-button {
    color: #fff;
    border-radius: 44rpx;
    padding: 15rpx 30rpx;
    background: linear-gradient(270deg, #ffe7c2 0%, #ffa926 100%);
  }
  .icon-class {
    width: 30rpx;
    padding-right: 10rpx;
    height: 30rpx;
  }
  .card-item {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 30rpx;
  }
  .item-name {
    padding-bottom: 30rpx;
    color: #fff;
    font-weight: 800;
    font-size: 34rpx;
  }
  .item-buttom {
    width: fit-content;
    display: flex;
    align-items: center;
  }
  .item-button {
    background-color: #1285ff;
    padding: 16rpx 30rpx;
    border-radius: 8rpx;
    color: #fff;
    font-size: 26rpx;
  }
  .item-title {
    display: flex;
    align-items: center;
    padding-left: 20rpx;
    color: #83919e;
  }
  .card-image {
    height: 170rpx;
    width: 150rpx;
    padding-right: 20rpx;
  }
}
</style>
