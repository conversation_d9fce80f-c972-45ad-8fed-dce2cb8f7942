<template>
  <view class="min-h-screen bg-[#f5f5f5]">
    <!-- 顶部个人信息区域 - 蓝色渐变背景 -->
    <view
      class="relative overflow-hidden"
      style="background-image: url('/static/profile/profile-bg.png')"
    >
      <!-- 状态栏占位 -->
      <view class="h-11"></view>

      <!-- 标题栏 -->
      <view class="flex items-center justify-between px-4 py-3">
        <text class="text-lg font-medium text-white">个人中心</text>
        <view class="flex items-center">
          <wd-icon name="more-horizontal" size="20px" color="white"></wd-icon>
          <wd-icon name="scan" size="20px" color="white" class="ml-3"></wd-icon>
        </view>
      </view>

      <!-- 用户信息 -->
      <view class="px-4 pb-6">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <wd-img
              :width="60"
              :height="60"
              round
              src="https://images.unsplash.com/photo-1487017159836-4e23ece2e4cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
              @click="handleUserClick"
            ></wd-img>
            <view class="ml-3">
              <view class="flex items-center mb-1" @click="handleUserClick">
                <text class="text-lg font-medium text-white mr-2">柚子</text>
                <wd-icon name="arrow-right" size="16px" color="white"></wd-icon>
              </view>
              <text class="text-sm text-white opacity-80">137****1459</text>
            </view>
          </view>

          <!-- 会员中心按钮 -->
          <view class="bg-black bg-opacity-20 rounded-full px-4 py-2" @click="handleMemberClick">
            <text class="text-sm text-white">会员中心</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 钱包信息卡片 -->
    <view class="mx-4 -mt-6 mb-4 relative z-10">
      <view class="bg-white rounded-2xl p-4 shadow-sm">
        <view class="flex">
          <view class="flex-1 text-center">
            <text class="text-2xl font-bold text-gray-900 block">509.50</text>
            <text class="text-sm text-gray-600">钱包余额</text>
          </view>
          <view class="w-px bg-gray-200 mx-4"></view>
          <view class="flex-1 text-center">
            <text class="text-2xl font-bold text-gray-900 block">5</text>
            <text class="text-sm text-gray-600">我的优惠券</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单列表 -->
    <view class="mx-4 mb-4">
      <view class="bg-white rounded-2xl overflow-hidden">
        <!-- 全部订单 -->
        <view
          class="flex items-center px-4 py-4 border-b border-gray-100"
          @click="handleMenuClick('orders')"
        >
          <wd-icon name="order" size="20px" color="#666"></wd-icon>
          <text class="ml-3 text-base text-gray-900 flex-1">全部订单</text>
          <wd-icon name="arrow-right" size="16px" color="#ccc"></wd-icon>
        </view>

        <!-- 专属客服 -->
        <view
          class="flex items-center px-4 py-4 border-b border-gray-100"
          @click="handleMenuClick('service')"
        >
          <wd-icon name="service" size="20px" color="#666"></wd-icon>
          <text class="ml-3 text-base text-gray-900 flex-1">专属客服</text>
          <wd-icon name="arrow-right" size="16px" color="#ccc"></wd-icon>
        </view>

        <!-- 在线反馈 -->
        <view
          class="flex items-center px-4 py-4 border-b border-gray-100"
          @click="handleMenuClick('feedback')"
        >
          <wd-icon name="chat" size="20px" color="#666"></wd-icon>
          <text class="ml-3 text-base text-gray-900 flex-1">在线反馈</text>
          <wd-icon name="arrow-right" size="16px" color="#ccc"></wd-icon>
        </view>

        <!-- 关于我们 -->
        <view
          class="flex items-center px-4 py-4 border-b border-gray-100"
          @click="handleMenuClick('about')"
        >
          <wd-icon name="info" size="20px" color="#666"></wd-icon>
          <text class="ml-3 text-base text-gray-900 flex-1">关于我们</text>
          <wd-icon name="arrow-right" size="16px" color="#ccc"></wd-icon>
        </view>

        <!-- 设置 -->
        <view class="flex items-center px-4 py-4" @click="handleMenuClick('settings')">
          <wd-icon name="setting" size="20px" color="#666"></wd-icon>
          <text class="ml-3 text-base text-gray-900 flex-1">设置</text>
          <wd-icon name="arrow-right" size="16px" color="#ccc"></wd-icon>
        </view>
		
		<view class="flex items-center px-4 py-4"   @click="openLogin()" v-if="is_test()">
		  <wd-icon name="setting" size="20px" color="#666"></wd-icon>
		  <text class="ml-3 text-base text-gray-900 flex-1">测试登陆</text>
		  <wd-icon name="arrow-right" size="16px" color="#ccc"></wd-icon>
		</view>
      </view>
    </view>

    <!-- VIP福利专区 -->
    <view class="mx-4 mb-8">
      <view class="mb-3">
        <text class="text-base font-bold text-gray-900">VIP福利专区</text>
        <text class="text-xs text-gray-500 ml-2">积分商城即将上线...</text>
      </view>
      <view class="bg-white rounded-2xl overflow-hidden">
        <wd-img src="/static/profile/vip-food.jpg" :height="120" width="100%" mode="aspectFill" />
        <view class="p-4">
          <text class="text-sm font-medium text-gray-900">VIP好货0元认领</text>
          <text class="text-xs text-gray-500 block mt-1">超值推荐</text>
          <text class="text-xs text-red-500 mt-2">免费领取</text>
        </view>
      </view>
    </view>
    <loginModel verify />
  </view>
</template>

<script setup>
	const is_test=()=>{
	 return	import.meta.env.VITE_USER_NODE_ENV=='development'
	}
	const openLogin = () => {
		uni.navigateTo({
			url: '/pages/login/test'
		});
	};
// 处理菜单点击事件
const handleMenuClick = (type) => {
  console.log('点击菜单:', type)
  // 根据不同类型跳转到对应页面
  switch (type) {
    case 'orders':
      uni.navigateTo({
        url: '/pages/orders/index',
      })
      break
    case 'feedback':
      uni.navigateTo({
        url: '/pages/feedback/index',
      })
      break
    case 'about':
      uni.navigateTo({
        url: '/pages/about/index',
      })
      break
    case 'service':
      uni.navigateTo({
        url: '/pages/service/index',
      })
      break
    case 'settings':
      uni.navigateTo({
        url: '/pages/settings/index',
      })
      break
  }
}

// 处理用户信息点击
const handleUserClick = () => {
  uni.navigateTo({
    url: '/pages/user-info/index',
  })
}

// 处理会员卡点击
const handleMemberClick = () => {
  uni.navigateTo({
    url: '/pages/member/index',
  })
}
</script>

<style scoped>
/* 点击态效果 */
.flex:active {
  opacity: 0.7;
  background-color: rgba(0, 0, 0, 0.05);
  transition: all 0.1s ease;
}

/* 卡片阴影效果 */
.shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 渐变背景增强 */
.bg-gradient-blue {
  background: linear-gradient(135deg, #4158d0 0%, #c850c0 100%);
}

/* 圆角卡片 */
.rounded-2xl {
  border-radius: 16px;
}
</style>
