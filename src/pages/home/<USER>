<template>
  <view class="min-h-screen bg-[#e6effb]">
    <!-- 顶部轮播图区域 -->
    <view class="relative">
      <at-swiper position="1" :height="240" custom-indicator-class="bottom-18!" />
    </view>

    <!-- 预订表单区域 -->
    <view class="bg-white mx-3 -mt-15 rounded-lg relative z-10 p-4">
      <view class="flex items-center justify-between mb-4 pb-3 border-b-solid border-[#F2F2F2]">
        <view class="flex items-center">
          <text class="text-lg font-medium text-gray-800">九江市</text>
        </view>
        <view class="flex items-center text-gray-500">
          <wd-img
            :width="16"
            :height="16"
            src="/static/home/<USER>"
            mode="aspectFit"
          />
          <text class="text-sm ml-1">我的位置</text>
        </view>
      </view>
      <view class="grid grid-cols-3 gap-2 mb-6">
        <view class="flex flex-col" @tap="handleDateCalendarVisible = true">
          <text class="text-xs text-gray-500 mb-1">入住</text>
          <text class="text-sm font-medium">
            {{ dayjs(dateCalendarValue.start).format('MM月DD日') }}
          </text>
          <text class="text-xs text-gray-400">周三</text>
        </view>
        <view class="flex flex-col items-center justify-center">
          <text class="text-xl font-bold text-[#23222f]">{{ dateCalendarValue.interval }}晚</text>
        </view>
        <view class="flex flex-col items-end" @tap="handleDateCalendarVisible = true">
          <text class="text-xs text-gray-500 mb-1">离店</text>
          <text class="text-sm font-medium">
            {{ dayjs(dateCalendarValue.end).format('MM月DD日') }}
          </text>
          <text class="text-xs text-gray-400">周日</text>
        </view>
      </view>

      <view class="text-center">
        <wd-button custom-class="w-[70%]" @tap="handleSearch">开始查找</wd-button>
      </view>
    </view>

    <!-- 住中服务区域 -->
    <view class="px-4 my-3">
      <view class="mb-4 pt-4.5">
        <text class="text-base text-[#272636]">住中服务</text>
        <view class="w-full h-px bg-[#D1D1D1] mt-2"></view>
      </view>

      <view class="grid grid-cols-5 gap-6 py-4 text-[#505153]">
        <view class="flex-col-center" @tap="handleMenuClick('orders')">
          <wd-img src="/static/home/<USER>" :width="36" :height="36" mode="aspectFit" />
          <text class="text-[8px] mt-2">客房送物</text>
        </view>
        <view class="flex-col-center" @tap="handleMenuClick('feedback')">
          <wd-img src="/static/home/<USER>" :width="36" :height="36" mode="aspectFit" />
          <text class="text-[8px] mt-2">商品购买</text>
        </view>
        <view class="flex-col-center" @tap="handleMenuClick('about')">
          <wd-img src="/static/home/<USER>" :width="36" :height="36" mode="aspectFit" />
          <text class="text-[8px] mt-2">一键WIFI</text>
        </view>
        <view class="flex-col-center" @tap="handleMenuClick('service')">
          <wd-img src="/static/home/<USER>" :width="36" :height="36" mode="aspectFit" />
          <text class="text-[8px] mt-2">一键开门</text>
        </view>
        <view class="flex-col-center" @tap="handleMenuClick('service')">
          <wd-img
            src="/static/home/<USER>"
            :width="36"
            :height="36"
            mode="aspectFit"
          />
          <text class="text-[8px] mt-2">在线客服</text>
        </view>
      </view>
    </view>

    <!-- 热门推荐区域 -->
    <view class="mx-4 mt-6 pb-10">
      <view class="mb-4 pt-4.5">
        <text class="text-base text-[#272636]">热门推荐</text>
        <view class="w-full h-px bg-[#D1D1D1] mt-2"></view>
      </view>
      <at-swiper position="1" :height="130" custom-item-class="rounded-lg" />
    </view>
    <date-calendar
      v-model:visible="handleDateCalendarVisible"
      @confirm="handleDateCalendarConfirm"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import DateCalendar from '@/components/date-calendar/index.vue'
import { dayjs } from '@/uni_modules/wot-design-uni/components/common/dayjs'
import { useHotel } from '/src/utils/hook/useHotel'
const { data, dateCalendarValue } = useHotel()
const handleDateCalendarVisible = ref(false)
const handleDateCalendarConfirm = (e) => {
  dateCalendarValue.value = e
  data.value = e
}
const handleSearch = () => {
  uni.navigateTo({
    url: '/pagesHotel/pages/hotel/list/index',
  })
}
// 处理服务点击
const handleService = (type) => {
  console.log('点击服务：', type)
  uni.showToast({
    title: `${getServiceName(type)}功能开发中`,
    icon: 'none',
  })
}

// 处理旅游推荐点击
const handleTravel = () => {
  console.log('点击旅游线路定制')
  uni.navigateTo({
    url: '/pages/travel/index',
  })
}

// 获取服务名称
const getServiceName = (type) => {
  const names = {
    room: '客房送物',
    shop: '商品购买',
    wifi: '一键WIFI',
    door: '一键开门',
    service: '在线客服',
  }
  return names[type] || '服务'
}

// 页面生命周期
onLoad(() => {
  console.log('首页加载完成')
})
</script>

<style scoped lang="scss">
// 自定义样式补充
.active:scale-98:active {
  transform: scale(0.98);
}

// swiper指示器自定义
::v-deep .uni-swiper-dots {
  bottom: 80px;
}

::v-deep .uni-swiper-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>
