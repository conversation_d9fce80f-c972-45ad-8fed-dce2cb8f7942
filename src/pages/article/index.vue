<template>
	<view>
		<view style="width: 100%; position: relative" v-if="datas.type == '2'">
			<image id="background-image111" style="width: 750rpx" mode="widthFix" :src="datas.image" @load="initLoad" ref="imgesRef"></image>
			<block v-if="datas.deploy && datas.deploy.length">
				<view
					v-for="(item, index) in datas.deploy"
					:key="index"
					@click="openLink(item.list)"
					style="position: absolute"
					:style="initStyle(item)"
				></view>
			</block>
		</view>
		<view style="padding: 30rpx" v-else>
			<view>{{ initText(datas.title) }}</view>
			<view class="time-class">{{ initText(datas.create_at) }}</view>

			<view>
				<wd-status-tip image="content" tip="暂无内容" v-if="!datas.id" />
				<u-parse :content="datas.content" v-else></u-parse>
			</view>
		</view>
		<view class="bottom-body-list">
			<view class="body-list-class" v-if="datas.is_button == '1' && is_receive">
				<view class="button-class" @click="openLink(datas.button_deploy)" v-if="is_receive == '1'">立即领取</view>
				<view class="button-class" style="background: #cccc" v-if="is_receive == '2'">已领取</view>
			</view>
		</view>

		<login-model></login-model>
	</view>
</template>

<script setup>
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import { getDetails } from './api/index.js';
import { useUser } from '/src/utils/hook/useUser.js';
const { isLogin } = useUser();
import { ref, getCurrentInstance, nextTick } from 'vue';
import uParse from '/src/components/u-parse/u-parse.vue';
const imgesRef = ref(null);
import { onChange,onShare } from '/src/utils/open-menu.js';
const { proxy } = getCurrentInstance();
const query = uni.createSelectorQuery().in(proxy);
const imgeStyle = ref({});
const activity_type = ref('');
const activity_id = ref('');
const article_id = ref('');
const is_receive = ref('');
const share_id = ref('');
onShareAppMessage(() => {
	onShare(share_id.value)
	return {
		title: '新云停车',
		path: `/pages/article/index?id=${article_id.value}`
	};
});

onLoad((e) => {
	share_id.value = e.share_id;
	// uni.setNavigationBarTitle({
	// 	title: '详情'
	// });

	activity_id.value = e.activity_id;
	activity_type.value = e.type;
	article_id.value = e.id;
	initData(e.id);
});

const openLink = (e) => {
	onChange({ article_id: datas.value.id, id: activity_id.value, activity_type: activity_type.value, deploy: e }, (e) => {
		initData(article_id.value);
	});
};
const initLoad = (e) => {
	imgeStyle.value = e.detail;
};

const bgWidth = 350;
const initStyle = (e) => {
	e = { ...e };
	// console.log(imgeStyle.value);
	if (imgeStyle.value.height) {
		return {
			left: `${pxToRpx(e.x, e.displayWidth)}px`,
			top: `${heightPxToRpx(e.y, e.displayHeight)}px`,
			width: `${pxToRpx(e.width, e.displayWidth)}px`,
			height: `${heightPxToRpx(e.height, e.displayHeight)}px`
		};
	} else {
		return {};
	}
};

const pxToRpx = (px, bg) => {
	const screenWidth = uni.getSystemInfoSync().screenWidth;
	const Ratio = screenWidth * (Number(px) / bg);
	return Ratio;
};
const heightPxToRpx = (px, bg) => {
	const screenHeight = uni.getSystemInfoSync().windowHeight;
	const screenWidth = uni.getSystemInfoSync().screenWidth;
	const Ratio = rpx2px(imgeStyle.value.height) * (Number(px) / bg);
	return Ratio;
};
const rpx2px = (height) => {
	return (uni.getSystemInfoSync().screenWidth * Number.parseInt(height)) / 750;
};
const initText = (e) => {
	if (e || e === 0) {
		return e;
	} else {
		return '-';
	}
};
const datas = ref({});

const initData = (e) => {
	getDetails({ id: e }).then((res) => {
		if (res.data && res.data.id) {
			// uni.setNavigationBarTitle({
			// 	title: res.data.title
			// });
		}
		is_receive.value = res.data.is_receive;
		datas.value = res.data;
	});
};
</script>

<style lang="scss" scoped>
.time-class {
	font-weight: 400;
	font-size: 28rpx;
	color: #999999;
	padding: 30rpx 0;
}

.bottom-body-list {
	width: 100%;
	position: fixed;
	bottom: 0;
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	.body-list-class {
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		margin-bottom: 10rpx;
	}
	.button-class {
		background: linear-gradient(90deg, #ffb45f 0%, #ff5f5f 100%);
		// margin-top: 30rpx;
		padding: 22rpx 0;
		display: flex;
		justify-content: center;
		font-weight: 500;
		font-size: 32rpx;
		color: #ffffff;
		border-radius: 120rpx;
	}
}
</style>
