<template>
  <view class="min-h-screen bg-[#edf5fc]">
    <!-- 顶部提示区域 -->
    <view class="bg-gray-200 px-4 py-3 text-center">
      <text class="text-sm text-gray-600">这里的当季热门项目较少报</text>
      <text class="text-sm text-gray-600 block mt-1">点击页面跳转到对应的链接浏览所有商品</text>
    </view>

    <!-- 服务卡片区域 -->
    <view class="px-4 mt-4">
      <view
        class="bg-blue-500 rounded-lg p-4 mb-3 flex items-center justify-between"
        @click="handleTravelCustom"
      >
        <view class="flex items-center">
          <view class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
            <text class="i-carbon-airplane text-blue-500"></text>
          </view>
          <text class="text-white font-medium">旅行定制、票务咨询</text>
        </view>
        <text class="i-carbon-chevron-right text-white"></text>
      </view>

      <view
        class="bg-blue-600 rounded-lg p-4 mb-3 flex items-center justify-between"
        @click="handleTicketService"
      >
        <view class="flex items-center">
          <view class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
            <text class="i-carbon-ticket text-blue-600"></text>
          </view>
          <text class="text-white font-medium">票务咨询</text>
        </view>
        <text class="i-carbon-chevron-right text-white"></text>
      </view>

      <text class="text-xs text-gray-600 text-center block mt-2">
        外地四客服预定不了，需要一对一定制服务!
      </text>
    </view>

    <!-- 近期上新 -->
    <view class="px-4 mt-6">
      <wd-tag
        custom-class="rounded-full! mb-2 font-bold text-[12px] px-2.5! py-1.5!"
        type="primary"
        bg-color="#4B71B6"
      >
        近期上新
      </wd-tag>

      <scroll-view scroll-x="true" class="whitespace-nowrap">
        <view class="flex space-x-3">
          <view
            v-for="(item, index) in 4"
            :key="index"
            class="flex-shrink-0 w-32 h-24 bg-gray-400 rounded-lg flex items-center justify-center"
          >
            <text class="text-white font-medium">景点</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 发现宝藏目的地 -->
    <view class="px-4 mt-4">
      <text class="text-base font-bold text-[#212121] block">发现宝藏目的地</text>
      <text class="text-[10px] text-[#6E6E6E] block">千座入云峰，万千旅行方式！</text>

      <view class="grid grid-cols-3 gap-3 mt-3">
        <view
          v-for="(item, index) in 3"
          :key="index"
          class="bg-white rounded-lg overflow-hidden shadow-sm"
          @click="handleDestination(index)"
        >
          <view class="h-20 bg-gradient-to-br from-blue-400 to-cyan-400 relative">
            <view class="absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded text-xs">
              景点包邮
            </view>
          </view>
          <view class="p-2">
            <text class="text-xs text-gray-800 block">【景点包邮】经济型酒店名称</text>
            <text class="text-red-500 font-bold text-sm mt-1">¥999</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门爆款 -->
    <view class="px-4 mt-4">
      <text class="text-base font-bold text-[#212121] block mb-4">热门爆款</text>

      <!-- 国际线路 -->
      <view class="mb-4">
        <text class="text-[10px] mb-3 text-[#6E6E6E] block">国际线路</text>
        <view
          class="bg-white rounded-lg overflow-hidden shadow-sm relative"
          @click="handleInternational"
        >
          <view class="h-32 bg-gradient-to-r from-orange-400 to-yellow-400 relative">
            <view class="absolute top-3 right-3 bg-red-500 text-white px-3 py-1 rounded-full">
              <text class="text-xs">推荐</text>
            </view>
            <view class="absolute bottom-3 left-3">
              <text class="text-white text-base font-medium">印度尼西亚、巴厘岛</text>
              <text class="text-white text-sm block">榜首7天5晚</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 国内线路 -->
      <view class="mb-4">
        <text class="text-[10px] mb-3 text-[#6E6E6E] block">国内线路</text>
        <view
          class="bg-white rounded-lg overflow-hidden shadow-sm relative"
          @click="handleDomestic"
        >
          <view class="h-32 bg-gradient-to-r from-blue-500 to-cyan-500 relative">
            <view class="absolute top-3 right-3 bg-yellow-500 text-white px-3 py-1 rounded-full">
              <text class="text-xs">超值</text>
            </view>
            <view class="absolute bottom-3 left-3">
              <text class="text-white text-base font-medium">南亚岛国、马尔代夫</text>
              <text class="text-white text-sm block">7天5晚自由行</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 旅行月历 -->
    <view class="px-4 mt-8 pb-20">
      <view class="mb-3">
        <view>
          <text class="text-base font-bold text-[#212121] block">旅行月历</text>
          <text class="text-[10px] text-[#6E6E6E] block">端午出行日历</text>
        </view>
      </view>

      <!-- 2025年历大图 -->
      <view class="bg-white rounded-lg overflow-hidden shadow-sm mb-4" @click="handleCalendar">
        <view
          class="h-40 bg-gradient-to-r from-blue-600 to-purple-600 relative flex items-center justify-center"
        >
          <text class="text-white text-xl font-bold">2025全年旅行日历</text>
        </view>
      </view>

      <!-- 季节小图 -->
      <view class="grid grid-cols-3 gap-3">
        <view class="bg-white rounded-lg overflow-hidden shadow-sm" @click="handleSeason('spring')">
          <view
            class="h-20 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center"
          >
            <text class="text-white text-sm">春</text>
          </view>
        </view>
        <view class="bg-white rounded-lg overflow-hidden shadow-sm" @click="handleSeason('summer')">
          <view
            class="h-20 bg-gradient-to-br from-yellow-400 to-orange-400 flex items-center justify-center"
          >
            <text class="text-white text-sm">夏</text>
          </view>
        </view>
        <view class="bg-white rounded-lg overflow-hidden shadow-sm" @click="handleSeason('winter')">
          <view
            class="h-20 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center"
          >
            <text class="text-white text-sm">冬</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 页面数据
const destinationData = ref([
  { name: '景点包邮酒店A', price: 999 },
  { name: '景点包邮酒店B', price: 999 },
  { name: '景点包邮酒店C', price: 999 },
])

// 处理旅行定制点击
const handleTravelCustom = () => {
  console.log('点击旅行定制')
  uni.showToast({
    title: '旅行定制功能开发中',
    icon: 'none',
  })
}

// 处理票务咨询点击
const handleTicketService = () => {
  console.log('点击票务咨询')
  uni.showToast({
    title: '票务咨询功能开发中',
    icon: 'none',
  })
}

// 处理目的地点击
const handleDestination = (index) => {
  console.log('点击目的地:', index)
  uni.showToast({
    title: `查看目的地详情`,
    icon: 'none',
  })
}

// 处理国际线路点击
const handleInternational = () => {
  console.log('点击国际线路')
  uni.showToast({
    title: '国际线路详情',
    icon: 'none',
  })
}

// 处理国内线路点击
const handleDomestic = () => {
  console.log('点击国内线路')
  uni.showToast({
    title: '国内线路详情',
    icon: 'none',
  })
}

// 处理日历点击
const handleCalendar = () => {
  console.log('点击旅行日历')
  uni.showToast({
    title: '查看完整日历',
    icon: 'none',
  })
}

// 处理季节点击
const handleSeason = (season) => {
  console.log('点击季节:', season)
  uni.showToast({
    title: `查看${season}季旅行推荐`,
    icon: 'none',
  })
}
</script>

<style scoped lang="scss">
// 横向滚动样式优化
::-webkit-scrollbar {
  display: none;
}

// 卡片交互效果
.shadow-sm:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}

// 标签样式增强
.bg-red-500,
.bg-yellow-500 {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
