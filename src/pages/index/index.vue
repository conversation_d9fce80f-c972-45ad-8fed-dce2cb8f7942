<template>
  <view class="page-body">
    <view class="page-background-body"></view>
    <view class="page-body-position" v-if="loading">
      <page-skeleton></page-skeleton>
    </view>
    <view class="page-body-position" v-show="!loading">
      <page-top></page-top>
      <page-notification></page-notification>
      <!-- 首页轮播 -->
      <at-swiper position="1"></at-swiper>

      <page-car-card></page-car-card>
      <wxadSwiper unit-id="adunit-36ca4d90f6112592" paddingTop="30rpx"></wxadSwiper>
      <page-menu-grid></page-menu-grid>

      <unlicensed-car-card></unlicensed-car-card>
      <!-- 首页添加 -->
      <at-recommend position="3"></at-recommend>
      <page-list></page-list>
      <!-- 首页弹窗 -->
      <at-overlay-swiper position="2"></at-overlay-swiper>
      <login-model :verify="true"></login-model>
    </view>
  </view>
</template>

<script setup>
import pageTop from './components/page-top.vue'
import pageNotification from './components/page-notification.vue'
import pageCarCard from './components/page-car-card.vue'
import unlicensedCarCard from './components/unlicensed-car-card.vue'
import pageMenuGrid from './components/page-menu-grid.vue'
import pageList from './components/page-list.vue'
import pageSkeleton from './components/page-skeleton.vue'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import { ref } from 'vue'
const share_id = ref('')
import { onShare } from '/src/utils/open-menu.js'
const loading = ref(true)
let interstitialAd = null
onLoad((e) => {
  // 在页面onLoad回调事件中创建插屏广告实例
  if (wx.createInterstitialAd) {
    interstitialAd = wx.createInterstitialAd({
      adUnitId: 'adunit-50f2157005b3f9d6',
    })
    interstitialAd.onLoad(() => {})
    interstitialAd.onError((err) => {
      console.error('插屏广告加载失败', err)
    })
    interstitialAd.onClose(() => {})
  }

  // 在适合的场景显示插屏广告
  if (interstitialAd) {
    interstitialAd.show().catch((err) => {
      console.error('插屏广告显示失败', err)
    })
  }

  share_id.value = e.share_id
  setTimeout(() => {
    loading.value = false
  }, 1500)
})
onShareAppMessage(() => {
  onShare(share_id.value)
  return {
    title: '新云停车',
    path: '/pages/index/index',
  }
})
</script>

<style scoped lang="scss">
.page-body {
  position: relative;
  background-color: transparent;
}
.page-body-position {
  position: relative;
  z-index: 1;
}
.page-background-body {
  position: absolute;
  width: 100%;
  height: 600rpx;
  background: linear-gradient(180deg, #e7f0fb, #fafafa);
}
</style>
