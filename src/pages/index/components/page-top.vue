<template>
	
	<view class="top-body" v-if="isLogin">
		<image v-if="user.avatar" :src="user.avatar" class="body-logo"></image>
		<image v-else src="@/static/avatar.jpg" class="body-logo"></image>
		<view class="body-center">
			<view class="body-center-title line1">{{ getTime() }}{{user.nickname}}</view>
			<view class="body-center-date">{{initDate()}} {{initDay()}}</view>
		</view>
	</view>
	<view class="top-body" v-else @click="setLogin()">
		<view class="you-logo">游</view>
		<view class="body-center">
			<view class="body-center-title line1">{{ getTime() }}游客</view>
			<view class="body-center-date">{{initDate()}} {{initDay()}}</view>
		</view>
	<!-- 	<view @click="openPage">
			<wd-badge :modelValue="1">
				<wd-icon name="notification" size="22px"></wd-icon>
			</wd-badge>
		</view> -->
	</view>
	
	
	
</template>

<script setup>
import { useUser } from '/src/utils/hook/useUser.js';
const { isLogin, setLogin,user } = useUser();
import {lg} from '/src/utils/utils.js'
const getTime = () => {
	const date = new Date();
	const hours = date.getHours();
	if (hours >= 0 && hours < 12) {
		return '上午好,';
	} else {
		return '下午好,';
	}
};
const initDate=()=>{
	return lg.dayjs().format('MM月DD日')
	
}
const initDay=()=>{
const currentDate = new Date();
const dayOfWeek = currentDate.getDay();
const daysOfWeek = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
return daysOfWeek[dayOfWeek];
}
const openPage = () => {
	uni.navigateTo({
		url: '/pagesB/pages/notification-list/index'
	});
};
</script>

<style lang="scss" scoped>
.top-body {
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.body-center {
		flex: 1 1 0;
		overflow-x: hidden;
		padding: 0 30rpx;
		.body-center-title {
			width: 100%;
			font-weight: bold;
			font-size: 44rpx;
		}
		.body-center-date {
			font-weight: 400;
			font-size: 24rpx;
			padding-top: 20rpx;
		}
	}
	.body-logo {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
	}
	.you-logo {
		font-weight: bold;
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 38rpx;
		color: #ffffff;
		background: #1285ff;
		border-radius: 50%;
	}
}
</style>
