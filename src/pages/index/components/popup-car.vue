<template>
    <wd-popup
        v-model="show"
        position="bottom"
        :safe-area-inset-bottom="true"
        custom-style="padding: 30rpx 40rpx;height:60%;min-height:400rpx;border-radius: 32rpx 32rpx 0 0;"
    >
        <view class="popup-body">
            <view class="popup-title">详情</view>
            <view class="popup-list">
                <scroll-view :scroll-y="true" style="height: 100%">
                    <view class="item-line">名称：{{initText(datas.parkname)}}</view>
                    <!-- <view class="item-line">总车位：{{initText(datas.totalspacenum)}}</view> -->
                    <view class="item-line">空闲车位：{{initText(datas.freespacenum) }}</view>
                    <view class="item-line lg-flex" @click="openMap">
                        <view style="flex-shrink: 0">地址：</view>
                        <view style="color:#1f8cff">
                            {{initText(datas.addr)}}
                      <image class="item-map-pupn" src="/static/index/map.png"></image>
                        </view>
                    </view>
                    <view class="item-line">{{ initText(datas.payrule) }}</view>
                </scroll-view>
            </view>
        </view>
    </wd-popup>
</template>

<script setup>
import { ref, watch } from 'vue';
const show = ref(false);
const datas = ref({});
watch(
    () => show.value,
    () => {
        if (show.value) {
            uni.hideTabBar({
                fail: function () {
                    console.log('失败');
                }
            });
        } else {
            uni.showTabBar({
                fail: function () {
                    console.log('失败');
                }
            });
        }
    }
);
const openShow = (e) => {
    datas.value = e;
    show.value = true;
};

const openMap = () => {
    uni.openLocation({
        latitude: Number(datas.value.lat),
        longitude: Number(datas.value.lon),
        scale: 12,
        name: datas.value.parkname,
        address: datas.value.addr
    });
};
const initText=(e)=>{
	if(e||e===0){
		return e
	}else{
		return '--'
	}
}
defineExpose({
    openShow
});
</script>

<style scoped lang="scss">
.item-map-pupn {
    height: 30rpx;
    width: 30rpx;
}
.popup-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    .popup-title {
        width: 100%;
        text-align: center;
    }
    .popup-list {
        flex: 1 1 0;
		font-size: 26rpx;
        overflow-y: hidden;
        padding-bottom: 30rpx;
        padding-top: 20rpx;
    }
    .popup-name {
        display: flex;
        align-items: center;
        color: #1285ff;
    }
    .item-body {
        padding: 30rpx 40rpx;
        margin: 20rpx 0;
        background: #f8f8f8;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
.item-line {
    padding: 15rpx 0;
}
.item-map {
    height: 30rpx;
    width: 30rpx;
    padding-right: 10rpx;
}
</style>
