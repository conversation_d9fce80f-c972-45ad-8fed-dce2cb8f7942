<template>
    <view class="list-bodys">
        <view class="lg-flex top-item">
            <view class="item-name">
                <image src="/static/biaoji.png" class="item-icon"></image>
                车场列表
            </view>
            <wd-picker :columns="seleteList" v-model="typeValue" use-default-slot @confirm="initData">
                <view class="item-rgiht">
                    <view>{{ typeValue == 1 ? '距离优先' : '车位优先' }}</view>
                    <image src="/static/index/shaixuan.png" class="item-shaixuan"></image>
                </view>
            </wd-picker>
        </view>
        <view class="list-card-body">
            <!--start -->
            <view class="gard-item" v-for="item in lists" :key="item.id" @click="onChange(item)">
                <view class="lg-flex item-top">
                    <view class="top-title">
                        <image class="item-p" src="/static/index/p_icon.png"></image>
                        {{ initText(item.parkname) }}
                    </view>
                    <view class="item-rgiht" @click.stop="openMap(item)">
                        <image class="item-map" src="/static/index/map.png"></image>
                        {{ initText(item.distance) }}km
                    </view>
                </view>
                <view class="item-line">
                   <!-- <view>
                        <wd-tag type="primary">
							 <view style="padding:6rpx 10rpx">总车位：{{ initText(item.totalspacenum) }}</view>
                        </wd-tag>
                    </view> -->
<!-- style="padding-left: 20rpx" -->
                    <view >
                        <wd-tag  type="success" >
                            <view style="padding:6rpx 10rpx" >空闲车位：{{ initText(item.freespacenum) }}</view>
                        </wd-tag>
                    </view>
                  
                </view>
                <view class="lg-flex">
                    <view class="tag-item line1">{{ initText(item.addr) }}</view>
                </view>
            </view>
            <!-- end -->
        </view>
        <lg-api :listApi="getCarParkPage" ref="lgApiRef" :afterData="afterData" noData @change="onData" />
        <popup-car ref="popupCarRef"></popup-car>
    </view>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import popupCar from './popup-car.vue';
import { onShow ,onPullDownRefresh} from '@dcloudio/uni-app';
import { getCarParkPage } from '../api/index.js';
const popupCarRef = ref(null);
const typeValue = ref(1);
const longitude = ref(null);
const latitude = ref(null);
const lgApiRef = ref(null);
const seleteList = [
    { label: '距离优先', value: 1 },
    { label: '车位优先', value: 2 }
];

onPullDownRefresh(() => {
	lgApiRef.value.initialize();
	lgApiRef.value.initData();
	setTimeout(function () {
		uni.stopPullDownRefresh();
	}, 500);
});
onShow(() => {
    nextTick(() => {
        // #ifdef MP-WEIXIN
        uni.getLocation({
            type: 'wgs84',
            success: (res) => {
                longitude.value = res.longitude;
                latitude.value = res.latitude;
                initData();
            },
            fail: (e) => {
                console.log('初始化坐标失败', e);
                initData();
            }
        });
        // #endif
        // #ifndef MP-WEIXIN
        initData();
        // #endif
    });
});

const initText = (e) => {
    if (e || e === 0) {
        return e;
    } else {
        return '--';
    }
};
const openMap = (e) => {
    uni.openLocation({
        latitude: Number(e.lat),
        longitude: Number(e.lon),
        scale: 12,
        name: e.parkname,
        address: e.addr
    });
};
const onChange = (e) => {
    popupCarRef.value.openShow(e);
};
const initData = () => {
	setTimeout(function () {
	lgApiRef.value.initialize();
	lgApiRef.value.initData();
	}, 200);
  
};

const afterData = (e) => {
    return { ...{ lon: longitude.value, lat: latitude.value, order: typeValue.value }, ...e };
};
const lists = ref([]);
const onData = (e) => {
    if (e.current == 1) lists.value = [];
    lists.value = [...lists.value, ...[].concat(e.records || [])];
};
</script>

<style scoped lang="scss">
.list-bodys {
    padding: 10rpx 30rpx;
    .top-item {
        justify-content: space-between;
    }
    .item-shaixuan {
        height: 30rpx;
        width: 30rpx;
        padding-left: 10rpx;
    }
    .item-map {
        height: 30rpx;
        width: 30rpx;
        padding-right: 10rpx;
    }
    .item-p {
        height: 40rpx;
        width: 40rpx;
        padding-right: 10rpx;
    }
    .item-rgiht {
        display: flex;
        align-items: center;
    }
    .item-name {
        display: flex;
        align-items: center;
        font-weight: bold;
        font-size: 32rpx;
        .item-icon {
            height: 30rpx;
            width: 50rpx;
            padding-right: 10rpx;
        }
    }
    .list-card-body {
        margin-top: 20px;
    }
    .item-line {
        font-size: 25rpx;
        padding: 20rpx 0;
        display: flex;
        align-items: center;
        .item-line-name {
            color: #1285ff;
        }
    }
    .gard-item {
        background-color: #ffffff;
        padding: 28rpx 32rpx;
        box-shadow: 0px 2rpx 20rpx rgb(51 54 74 / 4%);
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        .item-top {
            justify-content: space-between;
            .top-title {
                display: flex;
                align-items: center;
                font-weight: bold;
            }
        }
        .tag-item {
            // color: #1285ff;
            // background-colo r: #d9ebff;
            font-size: 24rpx;
            color: #999999;
            // padding: 10rpx;
            // margin-right: 20rpx;
        }
    }
}
</style>
