<template>
    <swiper style="height: 270rpx" indicator-dots indicator-active-color="#1285ff">
        <swiper-item v-for="item in lists" :key="item.id">
            <view class="page-car-card">
                <image src="@/static/index/car.png" class="card-image"></image>
                <view class="card-body-car">
                    <view class="car-bodys"  @click="openCar">
                        <view class="licence-plate">
                            {{ item.licence_plate }}
                        </view>
                        <view class="car-tag" :class="[`car-tag-${item.state}`]">
                            <view style="padding-right: 5rpx"><wd-icon name="secured" size="16px"></wd-icon></view>
                            {{ stateType[item.state] }}
                        </view>
                        <wd-icon name="chevron-right" size="18px"></wd-icon>
                    </view>

                    <view class="lg-flex discrepancy-title"  v-if="item.is_present=='1'">
                        <view class="discrepancy-success" v-if="item.is_pay=='1'" @click="onPay(item)">车辆已入场</view>
						<view class="discrepancy-warn" v-else>车辆待出场</view>
                        <view class="pay-class" v-if="item.entry_duration_minutes=='1'&&item.is_pay=='1'" @click="onPay(item)">
                            <view class="corner-class"></view>
                            <view style="position: relative; z-index: 2" >1个待支付</view>
                        </view>
                    </view>
					
					<view class="discrepancy-title discrepancy-info" v-else>车辆未入场</view>
                </view>
            </view>
        </swiper-item>
        <swiper-item v-if="isShow">
            <view class="page-car-card">
                <image src="@/static/index/car.png" class="card-image"></image>
                <view class="card-body" @click="openAddCar">
                    <view class="body-title">欢迎添加您的爱车~</view>
                    <view class="body-button">+ 添加车辆</view>
                </view>
            </view>
        </swiper-item>
    </swiper>
</template>

<script setup>
import { ref,onMounted,nextTick ,computed} from 'vue';
import { getVehicleHomeList } from '../api/index.js';
import { onShow,onPullDownRefresh } from '@dcloudio/uni-app';
import { useUser } from '/src/utils/hook/useUser.js';
const { isLogin, setLogin, user } = useUser();
const stateType = {
    1: '未认证',
    2: '已认证',
    3: '认证中',
    4: '认证失败'
};
onPullDownRefresh(()=>{
	nextTick(()=>{
		 initData();
	})
})
const onPay=(e)=>{
	if(e.entry_duration_minutes=='1'&&e.is_pay=='1'){
		uni.navigateTo({
		    url: `/pages/temporary-pay/index?park_id=${e.park_id}&licence_plate=${e.licence_plate}`
		});
	}
	
}
const isShow = computed(() => {
	if (maxSum.value === 0 || maxSum.value > lists.value.length) {
	    return true;
	} else {
	    return false;
	}
});
onShow(() => {
	nextTick(()=>{
		 initData();
	})
	
   
});
onMounted(()=>{
	 // initData();
})
const lists = ref([]);
const maxSum = ref(0);
const initData = () => {
	if(!isLogin.value) return
    getVehicleHomeList().then((res) => {
        maxSum.value = res.data.maxSum;
        lists.value = res.data.list;
    });
};

const openAddCar = () => {
	if(isLogin.value){
		uni.navigateTo({
		    url: '/pagesB/pages/car-manage/car-operate/index'
		});
	}else{
		uni.navigateTo({
		    url: '/pagesB/pages/replace-pay/index'
		});
		
	}
   
};
const openCar = () => {
    uni.navigateTo({
        url: '/pagesB/pages/car-manage/index'
    });
};
</script>

<style lang="scss" scoped>
.car-tag-1 {
    background-color: #ecf0f4;
    color: #a2adba;
}
.car-tag-2 {
    background-color: #d3f6e6;
    color: #06bb67;
}
.car-tag-3 {
    background-color: #fff1d7;
    color: #ffae17;
}
.car-tag-4 {
    background-color: #ffe6e6;
    color: #ff5f5f;
}
.discrepancy-title {
    font-weight: bold;
    font-size: 34rpx;
    padding-top: 40rpx;
}
.discrepancy-success {
    color: #06bb67;
}
.discrepancy-info {
    color: #a2adba;
}
.discrepancy-warn {
    color: #ffae17;
}
.pay-class {
    background-color: #ff5943;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    color: #fff;
    font-weight: 500;
    padding: 0 10rpx;
    position: relative;
    border-radius: 8rpx;
    margin-left: 30rpx;
}
.corner-class {
    position: absolute;
    width: 26rpx;
    height: 20rpx;
    background-color: #ff5943;
    left: -8rpx;
    top: 14rpx;
    transform: rotate(45deg);
    z-index: 1;
}
.page-car-card {
    background-color: #fff;
    margin: 30rpx;
    padding: 30rpx 0;
    display: flex;
    align-items: center;
    border-radius: 16rpx;
    .card-image {
        height: 180rpx;
        width: 250rpx;
    }

    .car-bodys {
        width: 100%;
        display: flex;
        padding-right: 30rpx;
        align-items: center;
        justify-content: flex-end;
        .licence-plate {
            font-weight: bold;
            font-size: 32rpx;
        }
        .car-tag {
            width: fit-content;
            display: flex;
            border-radius: 8rpx;
            align-items: center;
            padding: 10rpx;
            font-size: 20rpx;
            margin-left: 20rpx;
            margin-right: 20rpx;
        }
    }
    .card-body {
        flex: 1 1 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .card-body-car {
        flex: 1 1 0;
        height: 180rpx;
        display: flex;
        // align-items: flex-end;
        align-items: center;
        flex-direction: column;
        // justify-content: center;
    }
    .body-title {
        padding-bottom: 30rpx;
    }
    .body-button {
        background-color: #1285ff;
        padding: 16rpx 80rpx;
        border-radius: 8rpx;
        color: #fff;
        font-size: 26rpx;
    }
}
</style>
