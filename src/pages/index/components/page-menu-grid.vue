<template>
    <view class="page-grid" v-if="lists.length">
        <swiper :style="{ height: initClass() }" :indicator-dots="lists.length > 1" indicator-active-color="#1285FF">
            <swiper-item v-for="(items, index) in lists" :key="index">
                <wd-grid :column="4">
                    <wd-grid-item use-slot v-for="(item, index) in items" :key="item.id">
                        <view @click="onChange(item)">
                            <view>
                                <image :src="item.image" class="item-imge"></image>
                            </view>
                            <view class="item-title">{{ item.title }}</view>
                        </view>
                    </wd-grid-item>
                </wd-grid>
            </swiper-item>
        </swiper>
    </view>
</template>
<script>
export default {
    options: {
        styleIsolation: 'shared'
    }
};
</script>
<script setup>
import { onMounted, ref } from 'vue';
import { getQuickMenu } from '../api/index.js';
import { onChange } from '/src/utils/open-menu.js';
import { onShow,onPullDownRefresh } from '@dcloudio/uni-app';
onPullDownRefresh(()=>{
initData()
})

// const list = [
//   {name: '找车位', url: '/pagesB/pages/car-map/index', imge: '/static/index/zhaochewei.png'},
//   {name: '代缴费', url: '/pagesB/pages/replace-pay/index', imge: '/static/index/daijiaofei.png'},
//   {name: '月租卡', url: '/pagesB/pages/month-card/index', imge: '/static/index/yeuzhuka.png'},
//   {name: '营销中心', url: '/pagesB/pages/marketing-activities/index', imge: '/static/index/youhuiquan.png'}
// ];
const lists = ref([]);
const initData = () => {
    getQuickMenu().then((res) => {
        lists.value = chunkArray([].concat(res.data || []), 8);
    });
};
const initClass = () => {
    if (lists.value && lists.value.length) {
        if (lists.value.length > 1) {
            return '400rpx';
        } else if (lists.value[0].length > 4) {
            return '380rpx';
        }
    }
    return '180rpx';
};
onShow(() => {
    initData();
});
function chunkArray(array, chunkSize) {
    const result = [];
    for (let i = 0; i < array.length; i += chunkSize) {
        const chunk = array.slice(i, i + chunkSize);
        result.push(chunk);
    }
    return result;
}
</script>
<style lang="scss" scoped>
.page-grid {
    margin: 0 32rpx;
    padding: 0 20rpx;
    background-color: #fff;
    border-radius: 16rpx;
    margin-top: 20rpx;

    .item-imge {
        height: 80rpx;
        width: 80rpx;
        border-radius: 16rpx;
    }

    .item-title {
        padding-top: 10rpx;

        color: #333333;
    }
}
</style>
