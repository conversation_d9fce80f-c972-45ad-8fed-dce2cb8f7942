<template>
	<swiper
		v-if="lists.length"
		style="height: 500rpx"
		:circular="false"
		:indicator-dots="lists && lists.length > 1 ? true : false"
		indicator-active-color="#1285ff"
	>
		<swiper-item v-for="item in lists" :key="item.id">
			<view class="page-car-card">
				<view class="card-body-car">
					<view class="lg-flex litem-line-body">
						<view class="item-name">入场编号</view>
						<view class="item-text">{{ item.enter_code }}</view>
					</view>
					<view class="lg-flex litem-line-body">
						<view class="item-name">停车场</view>
						<view class="item-text line1">{{ item.parkname }}</view>
					</view>
					<view class="lg-flex litem-line-body">
						<view class="item-name">入场时间</view>
						<view class="item-text">{{ item.entry_time }}</view>
					</view>

					<view style="display: flex; justify-content: center; padding-bottom: 30rpx; padding-top: 30rpx">
						<view class="button-class" @click="openScanCode">
							<wd-icon name="qrcode" size="34rpx"></wd-icon>
							<text style="padding-left: 20rpx">扫码出场</text>
						</view>
					</view>
				</view>
			</view>
		</swiper-item>
	</swiper>
</template>

<script setup>
import { ref, nextTick, computed } from 'vue';
import { getUnlicensedList } from '../api/index.js';
import { onShow, onPullDownRefresh } from '@dcloudio/uni-app';
import { useUser } from '/src/utils/hook/useUser.js';
const { initCode } = useUser();

onPullDownRefresh(() => {
	nextTick(() => {
		initData();
	});
});

onShow(() => {
	nextTick(() => {
		initData();
	});
});
const openScanCode = () => {
	uni.scanCode({
		onlyFromCamera: true,
		success: function (res) {
			if (res.result) {
				uni.navigateTo({
					url: `/pages/temporary-pay/index?q=${res.result}`
				});
			} else {
				uni.showToast({
					icon: 'none',
					title: '二维码无效',
					duration: 2000
				});
			}
		}
	});
};
const lists = ref([]);
const initData = () => {
	initCode().then((e) => {
		getUnlicensedList({ openid: e.openid }).then((res) => {
			lists.value = res.data;
		});
	});
};
</script>

<style lang="scss" scoped>
.button-class {
	display: flex;
	align-items: center;
	color: #fff;
	background: #1285ff;
	padding: 20rpx 30rpx;
	border-radius: 100rpx;
}
.litem-line-body {
	padding: 30rpx 0;

	.item-name {
		color: #333333;
		font-weight: 400;
		font-size: 28rpx;
		padding-right: 10rpx;
	}
	.item-text {
		flex: 1 1 0;
		text-align: right;
	}
}
.page-car-card {
	background-color: #fff;
	margin: 30rpx;
	padding: 0 30rpx;
	display: flex;
	flex-direction: column;
	border-radius: 16rpx;
}
</style>
