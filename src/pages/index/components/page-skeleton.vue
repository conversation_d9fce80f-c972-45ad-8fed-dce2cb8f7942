<template>
    <view style="padding: 30rpx">
        <view style="display: flex">
            <wd-skeleton :row-col="[{ size: '48px', type: 'circle' }]" />
            <wd-skeleton :custom-style="{ width: '100%', marginLeft: '12px' }" :row-col="[{ width: '50%' }, { width: '100%' }]" />
        </view>
        <view style="padding: 30rpx 0">
            <wd-skeleton theme="text" />
        </view>
        <view>
            <wd-skeleton :row-col="[{ height: '260rpx' }]" />
        </view>
        <view style="padding: 30rpx 0">
            <wd-skeleton :row-col="grid" />
        </view>
		
		<view style="padding: 30rpx 0" v-for="item in 5" :key="item">
		    <wd-skeleton theme="paragraph" />
		</view>
    </view>
</template>

<script setup>
const grid = [
    [
        { width: '48px', height: '48px' },
        { width: '48px', height: '48px' },
        { width: '48px', height: '48px' },
        { width: '48px', height: '48px' },
        { width: '48px', height: '48px' }
    ],
    [
        { width: '48px', height: '16px' },
        { width: '48px', height: '16px' },
        { width: '48px', height: '16px' },
        { width: '48px', height: '16px' },
        { width: '48px', height: '16px' }
    ]
];
</script>
