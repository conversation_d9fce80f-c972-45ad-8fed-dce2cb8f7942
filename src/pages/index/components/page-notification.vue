<template>
	<view class="page-notification-body" v-if="textArray&&textArray.length">
		<view class="lg-flex boay-card" @click="openPage">
			<image src="/src/static/index/tomhzhi.png" class="left-icon"></image>
			<view class="rgiht-item"></view>
			<view class="item-text"><uv-notice-bar   direction="column" :text="textArray"  custom-class="space" /></view>
		</view>
	</view>
</template>
<script>
export default {
	options: {
		styleIsolation: 'shared'
	}
};
</script>
<script setup>
// getNotificationHomeList
import { onMounted, ref,computed } from 'vue';
import { getNotificationHomeList } from '../api/index.js';
const lists = ref([]);
import {onShow ,onPullDownRefresh } from '@dcloudio/uni-app';
onPullDownRefresh(()=>{
	initData();
})
const initData = () => {
    getNotificationHomeList().then((res) => {
        lists.value =[].concat(res.data || [])
    });
};
onShow(() => {
    initData();
});
const textArray =computed(()=>{
	return lists.value
	
})
const openPage = () => {
	uni.navigateTo({
		url: '/pagesB/pages/notification-list/index'
	});
};
</script>

<style lang="scss" scoped>

	:deep(.wd-notice-bar) {
		background: transparent !important;
		color: #1f273b !important;
		padding:9px 0px 9px 0px !important;
		font-size:24rpx !important;
		width: 100%;
	}
.page-notification-body {
	padding: 0rpx 30rpx 30rpx 30rpx;
	.boay-card {
		background: #fff;
		border-radius: 12rpx;
		padding: 16rpx 24rpx;
		align-items: center;
	}
	.left-icon {
		height: 60rpx;
		width: 60rpx;
		flex-shrink: 0;
	}
	.rgiht-item {
		width: 4rpx;
		margin: 0 30rpx;
		height: 40rpx;
		flex-shrink: 0;
		background: #dddce1;
	}
	.item-text {
		flex: 1 1 0;
		width: 100%;
		overflow: hidden;
		display: flex;
	}
}
</style>
