
import { request } from '/src/utils/request/index.js';
// 获取菜单列表
export async function getQuickMenu(payload={}) {
    return await request('/applet/tourist/operation/quickMenu/getList', { data: payload });
}

// 获取停车场列表
export async function getCarParkPage(payload={}) {
    return await request('/applet/tourist/car/carPark/getPage', { data: payload });
}

// 获取车辆列表
export async function getVehicleHomeList(payload={}) {
    return await request('/applet/vehicle/getHomeList', { data: payload });
}

// 通知公告
export async function getNotificationHomeList(payload={}) {
    return await request('/applet/tourist/operation/notification/getHomeList', { data: payload });
}

// 获取无牌车订单列表
export async function getUnlicensedList(payload={}) {
    return await request('/applet/tourist/car/order/getUnlicensedList', { data: payload });
}
