import { request } from '/src/utils/request/index.js'
// 酒店详情
export async function getHotelDetail(payload = {}) {
  return await request('/applet/tourist/hotel/getDetail', { data: payload })
}

// 商品列表
export async function getGoodsList(payload = {}) {
  return await request('/applet/tourist/mall/goods/getPage', { data: payload })
}
// 商品类型
export async function getGoodsTypeList(payload = {}) {
  return await request('/applet/tourist/mall/goodsType/getList', { data: payload })
}

// 通过购物车ID获取商品信息
export async function getGoodsDetailByIds(payload = {}) {
  return await request('/applet/tourist/mall/goods/getList', { data: payload })
}

// 获取单个商品详情
export async function getGoodsDetail(payload = {}) {
  return await request('/applet/tourist/mall/goods/getDetail', { data: payload })
}

// 提交订单
export async function submitOrder(payload = {}) {
  return await request('/applet/orderManage/order/setMallOrder', { data: payload })
}
// 验证订单
export async function setOrderVerify(payload = {}) {
  return await request('/applet/orderManage/order/setOrderVerify', { data: payload })
}

// 获取可用优惠券
export async function getOrderCoupon(payload = {}) {
  return await request('/applet/coupon/myCoupon/getOrderCoupon', { data: payload })
}

// (1/配送订单，2,自提订单)
export const orderType = [
  {
    label: '配送订单',
    value: 1,
  },
  {
    label: '自提订单',
    value: 2,
  },
]
