<template>
  <view class="shop-detail bg-#fafafa min-h-screen">
    <!-- z-paging-swiper容器 -->
    <z-paging-swiper>
      <!-- 顶部Tab栏 -->
      <template #top>
        <view class="shop-tabs-container bg-white">
          <wd-tabs v-model="activeTab" @change="handleTabChange">
            <wd-tab
              v-for="(category, index) in categories"
              :key="category.id"
              :title="category.name"
              :name="index"
            />
          </wd-tabs>
        </view>
      </template>

      <!-- swiper容器 -->
      <swiper
        class="h-full"
        :current="current"
        @transition="swiperTransition"
        @animationfinish="swiperAnimationfinish"
      >
        <swiper-item
          class="swiper-item h-full"
          v-for="(category, index) in categories"
          :key="category.id"
        >
          <!-- z-paging-swiper-item组件 -->
          <z-paging-swiper-item
            :ref="setSwiperItemRef(index)"
            :tab-index="index"
            :current-index="current"
            @query="onQuery"
          >
            <!-- 商品列表内容 -->
            <view class="goods-content bg-#fafafa p-4">
              <!-- 商品网格展示 -->
              <view
                v-if="goodsData[category.id] && goodsData[category.id].length > 0"
                class="grid grid-cols-2 gap-4"
              >
                <view
                  v-for="product in goodsData[category.id]"
                  :key="product.id"
                  class="bg-white rounded-lg overflow-hidden"
                  @click="viewProductDetail(product)"
                >
                  <!-- 商品图片 -->
                  <view class="relative">
                    <wd-img
                      :src="product.imge.split(',')[0]"
                      mode="aspectFill"
                      width="100%"
                      height="180rpx"
                      radius="8rpx 8rpx 0 0"
                    >
                      <template #error>
                        <view class="w-full h-180rpx bg-#f5f5f5 flex items-center justify-center">
                          <text class="text-#ccc text-xs">暂无图片</text>
                        </view>
                      </template>
                    </wd-img>
                  </view>

                  <!-- 商品信息 -->
                  <view class="p-3">
                    <!-- 商品名称 -->
                    <view class="text-#333 text-ellipsis-2 text-28rpx mb-2">
                      {{ product.name }}
                    </view>

                    <!-- 价格和添加按钮 -->
                    <view class="flex items-center justify-between">
                      <view class="text-red-500 text-32rpx">￥{{ product.price }}</view>

                      <wd-button
                        custom-class="w-48rpx! h-48rpx! bg-#4979dc! rounded-full"
                        @click.stop="addToCart(product)"
                        type="icon"
                        icon="add"
                        size="small"
                        custom-style="--wot-button-icon-color:#fff; padding: 0;"
                      />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </z-paging-swiper-item>
        </swiper-item>
      </swiper>
    </z-paging-swiper>

    <!-- 悬浮购物车 -->
    <ShopCar />
  </view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getGoodsTypeList, getGoodsList } from '@/pagesShop/api/index.js'
import { useShopCar } from '@/utils/hook/useShopCar'
import ShopCar from '@/components/shop-car.vue'

const { addCar } = useShopCar()

// 当前激活的标签索引
const activeTab = ref(0)
// 当前swiper索引
const current = ref(0)
// 分类数据
const categories = ref([])
// 商品数据（按分类ID分组）
const goodsData = reactive({})
// 页面初始化完成标识
const isInitialized = ref(false)
// swiper-item引用数组
const swiperItemRefs = ref([])

// 设置swiper-item引用
const setSwiperItemRef = (index) => (el) => {
  if (el) {
    swiperItemRefs.value[index] = el
  }
}

// 页面加载
onLoad(async (options) => {
  uni.setNavigationBarTitle({
    title: options.name || '店铺详情',
  })
  await initCategories()
})

// 初始化分类数据
const initCategories = async () => {
  try {
    const res = await getGoodsTypeList()
    if (res && res.data && res.data.length > 0) {
      categories.value = res.data
      // 初始化商品数据
      categories.value.forEach((category) => {
        goodsData[category.id] = []
      })
      // 标记初始化完成
      isInitialized.value = true
    } else {
      uni.showToast({
        title: '获取分类失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    uni.showToast({
      title: '获取分类失败',
      icon: 'none',
    })
  }
}

// 标签切换处理
const handleTabChange = ({ name }) => {
  current.value = name
}

// swiper滑动中
const swiperTransition = (e) => {
  // 可在此处添加滑动时的处理逻辑
}

// swiper滑动结束
const swiperAnimationfinish = (e) => {
  current.value = e.detail.current
  activeTab.value = e.detail.current
}

// z-paging-swiper-item查询事件
const onQuery = async (pageNo, pageSize) => {
  // 确保初始化完成且有分类数据
  if (!isInitialized.value || !categories.value.length) {
    const currentRef = swiperItemRefs.value[current.value]
    if (currentRef) {
      currentRef.complete([], false)
    }
    return
  }

  const currentCategory = categories.value[current.value]
  if (!currentCategory) {
    const currentRef = swiperItemRefs.value[current.value]
    if (currentRef) {
      currentRef.complete([], false)
    }
    return
  }

  try {
    const params = {
      current: pageNo,
      size: pageSize,
      goods_type_id: currentCategory.id,
    }
    const res = await getGoodsList(params)

    if (res && res.data && res.data.records) {
      // 处理商品数据
      const processedData = res.data.records
      // 更新商品数据
      if (pageNo === 1) {
        goodsData[currentCategory.id] = processedData
      } else {
        goodsData[currentCategory.id] = [...goodsData[currentCategory.id], ...processedData]
      }

      // 完成分页
      const currentRef = swiperItemRefs.value[current.value]
      if (currentRef) {
        currentRef.complete(processedData)
      }
    } else {
      // 无数据时的处理
      if (pageNo === 1) {
        goodsData[currentCategory.id] = []
      }
      const currentRef = swiperItemRefs.value[current.value]
      if (currentRef) {
        currentRef.complete([], false)
      }
    }
  } catch (error) {
    console.error('加载商品失败:', error)
    const currentRef = swiperItemRefs.value[current.value]
    if (currentRef) {
      currentRef.complete(false)
    }
    uni.showToast({
      title: '加载商品失败',
      icon: 'none',
    })
  }
}

// 查看商品详情
const viewProductDetail = (product) => {
  uni.navigateTo({
    url: `/pagesShop/pages/good-detail/index?id=${product.id}`,
  })
}

// 添加到购物车
const addToCart = (product) => {
  addCar({ id: product.id, count: 1 })
}
</script>
