<template>
  <view class="order-detail bg-#fafafa min-h-screen pb-20">
    <view class="m-4 p-4 bg-white rounded-lg">
      <view class="flex items-center mb-3">
        <wd-img src="/pagesShop/static/mall.png" width="40rpx" height="40rpx" custom-class="mr-2" />
        <view class="flex items-center flex-1">
          <text class="text-28rpx font-medium text-#333">{{ orderData.hotelName }}</text>
        </view>
      </view>
      <view v-for="(item, index) in orderData.goodsList" :key="item.id" class="flex mb-3">
        <!-- 商品图片 -->
        <wd-img
          :src="item.imge.split(',')[0]"
          width="210rpx"
          height="192rpx"
          radius="8rpx"
          mode="aspectFill"
        >
          <template #error>
            <view class="w-210rpx h-192rpx bg-#f5f5f5 flex items-center justify-center rounded-lg">
              <text class="text-#ccc text-xs">暂无图片</text>
            </view>
          </template>
        </wd-img>
        <!-- 商品信息 -->
        <view class="flex-1 flex flex-col justify-between pl-3">
          <view class="flex flex-col gap-2 text-32rpx">
            <view class="text-#333 text-ellipsis-2">
              {{ item.name }}
            </view>

            <view class="text-red-500">￥{{ item.price }}</view>
          </view>

          <!-- 数量调节器 -->
          <wd-input-number
            v-model="item.sum"
            :min="1"
            :max="item.goods_sum"
            :step="1"
            size="small"
          />
        </view>
      </view>
    </view>

    <wd-form ref="form" :model="formData" errorType="toast">
      <wd-cell-group
        :border="true"
        title="配送信息"
        custom-class="m-4 bg-white rounded-lg overflow-hidden"
      >
        <wd-picker
          label="配送方式"
          label-width="100px"
          prop="order_type"
          v-model="formData.order_type"
          :columns="orderType"
          required
          align-right
          :rules="[{ required: true, message: '请选择配送方式' }]"
        />
        <wd-cell
          required
          v-if="formData.order_type === 1"
          title="门店名称"
          :value="orderData.hotelName + '前台'"
        />
        <wd-cell
          required
          v-if="formData.order_type === 1"
          title="门店地址"
          :value="orderData.address"
          title-width="80px"
        />
        <wd-input
          required
          v-if="formData.order_type === 2"
          label="房间编号"
          label-width="100px"
          prop="room_code"
          align-right
          placeholder="请输入房间编号"
          v-model="formData.room_code"
          :rules="[{ required: true, message: '请输入房间编号' }]"
        />

        <wd-input
          label="联系方式"
          label-width="100px"
          prop="telephone"
          v-model="formData.telephone"
          placeholder="请输入联系方式"
          align-right
          :rules="[
            { required: true, message: '请输入联系方式' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系电话' },
          ]"
        />
      </wd-cell-group>
    </wd-form>
    <!-- 优惠折扣 -->
    <view class="bg-white mx-4 mt-4 rounded-lg">
      <text class="text-32rpx font-medium text-#333 mb-3 block px-4 pt-3">优惠折扣</text>
      <wd-cell title="优惠券" is-link @click="handleCouponClick">
        <text class="text-red-500" v-if="selectedCoupon.id">
          - ¥{{ discountAmount.toFixed(2) }}
        </text>
        <text v-else>请选择</text>
      </wd-cell>
    </view>

    <popup-detail ref="popupDetailRef" @change="handleCouponChange"></popup-detail>

    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 pb-safe">
      <view class="flex items-center justify-between">
        <!-- 价格信息 -->
        <view class="flex flex-col px-4">
          <view class="flex items-end">
            <text class="text-red-500 text-lg font-bold mr-1">¥{{ finalTotalPrice }}</text>
          </view>
          <text class="text-xs text-gray-400 line-through">¥{{ originalTotalPrice }}</text>
        </view>

        <!-- 提交按钮 -->
        <wd-button
          type="primary"
          size="large"
          @click="handleSubmitOrder"
          custom-style="width: 60%;height-100%;--wot-button-large-radius:0px;"
          :round="false"
          :loading="loading"
        >
          提交订单
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { throttle } from '/src/utils/utils.js'
import {
  submitOrder,
  setOrderVerify,
  getOrderCoupon,
  getGoodsDetailByIds,
  getHotelDetail,
  orderType,
} from '@/pagesShop/api/index.js'
import popupDetail from '@/components/popup-detail.vue'

// 订单数据
const orderData = reactive({
  hotelName: '酒店',
  hotelId: '',
  address: '',
  goodsList: [],
})

// 表单数据
const formData = reactive({
  order_type: 1,
  room_code: '',
  telephone: '',
})

const availableCoupons = ref([])
const selectedCoupon = ref({})
const submitting = ref(false)

const form = ref()

// 页面加载
onLoad(async (options) => {
  await getHotelDetailById(options.hotel_id)
  const cart_items = options.cart_items ? JSON.parse(options.cart_items) : []
  await getGoodsDetail(cart_items)
})
const popupDetailRef = ref(null)
const handleCouponClick = async () => {
  await loadAvailableCoupons()
  popupDetailRef.value?.openShow(availableCoupons.value, originalTotalPrice.value)
}

const getGoodsDetail = async (cart_items) => {
  const res = await getGoodsDetailByIds({ ids: cart_items.map((item) => item.id) })
  orderData.goodsList = res.data.map((item) => {
    const cart_item = cart_items.find((cart_item) => cart_item.id === item.id)
    return {
      ...item,
      sum: cart_item.count,
    }
  })
}

const getHotelDetailById = async (hotel_id) => {
  const res = await getHotelDetail({ id: hotel_id })
  orderData.hotelName = res.data.name
  orderData.hotelId = res.data.id
  orderData.address = res.data.address
}

// 加载可用优惠券
const loadAvailableCoupons = async () => {
  try {
    const res = await getOrderCoupon({ scene: '2', hotel_id: orderData.hotelId })
    if (res && res.data) {
      availableCoupons.value = res.data
    }
  } catch (error) {
    console.error('获取优惠券失败:', error)
  }
}

// 确认选择优惠券
const handleCouponChange = (e, data) => {
  selectedCoupon.value = data
}

// 计算原价总额
const originalTotalPrice = computed(() => {
  const originalTotalPrice = orderData.goodsList
    .reduce((total, item) => {
      return total + parseFloat(item.price) * item.sum
    }, 0)
    .toFixed(2)
  if (originalTotalPrice < 0.01) {
    return 0.01
  }
  if (
    selectedCoupon.value.type === '2' &&
    originalTotalPrice < parseFloat(selectedCoupon.value.doorsill_money)
  ) {
    selectedCoupon.value = {}
  }
  return originalTotalPrice
})

// 计算优惠金额
const discountAmount = computed(() => {
  if (!selectedCoupon.value.id) return 0

  const originalPrice = parseFloat(originalTotalPrice.value)
  const couponType = selectedCoupon.value.type

  switch (couponType) {
    case '1':
      return parseFloat(selectedCoupon.value.money)

    case '2':
      return parseFloat(selectedCoupon.value.money)

    case '3':
      const discount = parseFloat(selectedCoupon.value.discount) // 假设discount是折扣率，如0.8表示8折
      return originalPrice * (1 - discount)

    default:
      return 0
  }
})

// 计算最终价格
const finalTotalPrice = computed(() => {
  const total = parseFloat(originalTotalPrice.value) - discountAmount.value
  return Math.max(total, 0.01).toFixed(2)
})

const loading = ref(false)

// 提交订单
const handleSubmitOrder = async () => {
  // 节流
  throttle(() => {
    form.value.validate().then(async ({ valid, errors }) => {
      if (valid) {
        loading.value = true
        const orderParams = {
          hotel_id: orderData.hotelId,
          room_code: formData.order_type === 1 ? undefined : formData.room_code,
          order_type: formData.order_type,
          telephone: formData.telephone,
          goods_list: orderData.goodsList.map((item) => ({
            id: item.id,
            sum: item.sum,
          })),
          coupon_id: selectedCoupon.value.id || '',
        }
        uni.showLoading({ mask: true })
        setTimeout(function () {
          uni.hideLoading()
        }, 10000)

        submitOrder(orderParams)
          .then((res) => {
            const { order_code, appId, nonceStr, paySign, signType, timeStamp, delay_time } =
              res.data
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: timeStamp,
              nonceStr: nonceStr,
              package: res.data.package,
              signType: signType,
              paySign: paySign,
              success() {
                setTimeout(() => {
                  setOrderVerify({ out_trade_no: order_code })
                  openPage(1, '支付成功')
                }, 500)
              },
              fail(e) {
                setTimeout(() => {
                  uni.showToast({
                    icon: 'none',
                    title: '取消支付',
                    duration: 2000,
                  })
                }, 200)
              },
              complete() {
                loading.value = false
              },
            })
          })
          .catch((err) => {
            openPage(2, err.msg)
          })
          .finally(() => {
            uni.hideLoading()
          })
      }
    })
  })
}

const openPage = (state, msg) => {
  if (state == 2) {
    uni.navigateTo({
      url: `/pages/pay-state?state=${state}&msg=${msg}`,
    })
  } else {
    uni.redirectTo({
      url: `/pages/pay-state?state=${state}&msg=${msg}`,
    })
  }
}
</script>
