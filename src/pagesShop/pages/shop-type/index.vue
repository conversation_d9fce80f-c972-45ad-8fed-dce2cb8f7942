<template>
  <view class="h-screen flex flex-col bg-#f9f9f9">
    <wd-search
      v-model="searchKeyword"
      @input="onSearchInput"
      placeholder="请输入商品名称"
      :light="true"
      cancel-txt="搜索"
      custom-class="bg-#f9f9f9!"
    />
    <view class="flex-1 flex gap-3">
      <!-- 左侧分类导航 -->
      <wd-sidebar v-model="activeCategory" class="category-sidebar flex-shrink-0">
        <wd-sidebar-item
          v-for="category in categories"
          :key="category.id"
          :value="category.id"
          :label="category.name"
        />
      </wd-sidebar>

      <!-- 右侧商品列表 -->
      <view class="flex-1 bg-white">
        <z-paging
          ref="paging"
          v-model="productList"
          @query="loadProductList"
          :auto="false"
          :fixed="false"
          :default-page-size="10"
          :refresher-enabled="true"
          :loading-more-enabled="true"
          empty-view-text="暂无商品数据"
        >
          <!-- 商品网格布局 -->
          <view class="p-3 gap-3">
            <view
              v-for="product in productList"
              :key="product.id"
              class="flex"
              @click="viewProductDetail(product)"
            >
              <!-- 商品图片 -->
              <view class="product-image">
                <wd-img
                  :src="product.imge.split(',')[0]"
                  mode="aspectFill"
                  :lazy-load="true"
                  width="140rpx"
                  radius="8rpx"
                  height="140rpx"
                >
                  <template #error>
                    <view
                      class="error-wrap w-full h-140rpx bg-#f6f6f6 flex items-center justify-center"
                    >
                      <text class="text-#ccc text-xs">暂无图片</text>
                    </view>
                  </template>
                </wd-img>
              </view>

              <!-- 商品信息 -->
              <view class="p-2 flex justify-between flex-col flex-1">
                <!-- 商品名称 -->
                <view class="text-#333 text-ellipsis-2 text-30rpx">
                  {{ product.name }}
                </view>
                <!-- 价格区域 -->
                <view class="flex items-center justify-between w-full">
                  <view class="text-#142b86 flex gap-2 items-end">
                    <view class="text-28rpx">￥{{ product.price }}</view>
                    <view class="line-through text-24rpx">￥{{ product.original_price }}</view>
                  </view>

                  <!-- 购买按钮 -->
                  <wd-button
                    custom-class="w-40rpx! h-40rpx! bg-#4979dc! rounded-full"
                    @click.stop="addToCart(product)"
                    type="icon"
                    icon="add"
                    size="small"
                    custom-style="--wot-button-icon-color:#fff"
                  ></wd-button>
                </view>
              </view>
            </view>
          </view>
        </z-paging>
      </view>
    </view>
  </view>
  <ShopCar />
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { getGoodsList, getGoodsTypeList } from '../../api/index.js'
import { onLoad } from '@dcloudio/uni-app'
import ShopCar from '@/components/shop-car.vue'
import { useShopCar } from '/src/utils/hook/useShopCar'
const { addCar } = useShopCar()

const activeCategory = ref('')
const categories = ref([])
const productList = ref([])
const paging = ref(null)
const searchKeyword = ref('')

// 页面加载
onLoad(async (options) => {
  activeCategory.value = options.id || ''
  await initData()
})

// 初始化数据
const initData = async () => {
  try {
    const res = await getGoodsTypeList()
    if (res && res.data) {
      categories.value = res.data
      // 如果没有传入分类ID，使用第一个分类
      if (!activeCategory.value && categories.value.length > 0) {
        activeCategory.value = categories.value[0].id
      }
      // 延迟加载商品列表，确保组件已渲染
      setTimeout(() => {
        if (paging.value) {
          paging.value.reload()
        }
      }, 100)
    }
  } catch (error) {
    console.error('获取商品分类失败:', error)
    uni.showToast({
      title: '获取分类失败',
      icon: 'none',
    })
  }
}

// 加载商品列表
const loadProductList = (pageNo, pageSize) => {
  // 参数验证
  if (!activeCategory.value) {
    console.warn('未选择商品分类')
    if (paging.value) {
      paging.value.complete([], true)
    }
    return
  }

  const params = {
    current: pageNo,
    size: pageSize,
    goods_type_id: activeCategory.value,
  }

  // 添加搜索关键词
  if (searchKeyword.value && searchKeyword.value.trim()) {
    params.name = searchKeyword.value.trim()
  }

  getGoodsList(params)
    .then((res) => {
      if (res && res.data && res.data.records) {
        // 处理商品数据，确保必要字段存在
        const processedData = res.data.records.map((item) => ({
          ...item,
          image: item.image || item.imge || '', // 兼容不同的图片字段名
          price: item.price || '0',
          originalPrice: item.originalPrice || item.original_price || '',
        }))

        if (paging.value) {
          // 判断是否还有更多数据
          const hasMore = res.data.current < res.data.pages
          paging.value.complete(processedData, !hasMore)
        }
      } else {
        console.warn('商品数据格式异常:', res)
        if (paging.value) {
          paging.value.complete([], true)
        }
      }
    })
    .catch((error) => {
      console.error('获取商品列表失败:', error)
      if (paging.value) {
        paging.value.complete([], true)
      }
      uni.showToast({
        title: '加载商品失败',
        icon: 'none',
      })
    })
}

// 监听分类切换
watch(activeCategory, (newCategory) => {
  if (newCategory) {
    // 清空搜索关键词
    searchKeyword.value = ''
    // 重新加载商品列表
    if (paging.value) {
      paging.value.reload()
    }
  }
})

// 搜索输入处理
const onSearchInput = () => {
  // 延迟搜索，避免频繁请求
  setTimeout(() => {
    if (paging.value) {
      paging.value.reload()
    }
  }, 300)
}

// 查看商品详情
const viewProductDetail = (product) => {
  uni.navigateTo({
    url: `/pagesShop/pages/good-detail/index?id=${product.id}`,
  })
}

// 添加到购物车
const addToCart = (product) => {
  addCar({ id: product.id, count: 1 })
}
</script>

<style></style>
