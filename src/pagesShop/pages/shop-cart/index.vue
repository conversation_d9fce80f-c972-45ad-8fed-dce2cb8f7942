<template>
  <view class="cart-page bg-#fafafa min-h-screen flex flex-col">
    <!-- 购物车内容 -->
    <view class="flex-1">
      <!-- 购物车为空 -->
      <view v-if="cartItems.length === 0" class="flex flex-col items-center justify-center py-20">
        <wd-icon name="cart" size="64px" color="#ccc" class="mb-4" />
        <text class="text-#999 text-base mb-6">购物车空空如也</text>
        <wd-button type="primary" size="medium" @click="goShopping">去逛逛</wd-button>
      </view>

      <!-- 购物车商品列表 -->
      <view v-else>
        <!-- 按酒店分组显示 -->
        <view v-for="(items, hotel_name) in groupedItems" :key="hotel_name" class="mb-4">
          <!-- 酒店分组头部 -->
          <view class="m-4 p-4 bg-white rounded-lg">
            <view class="flex items-center mb-3">
              <wd-img
                src="/pagesShop/static/mall.png"
                width="40rpx"
                height="40rpx"
                custom-class="mr-2"
              />
              <view class="flex items-center flex-1">
                <text class="text-28rpx font-medium text-#333">{{ hotel_name }}</text>
              </view>
              <text
                class="text-28rpx text-#244190"
                @click="handleHotelSelectChange(hotel_name, !isHotelAllSelected(hotel_name))"
              >
                全选
              </text>
            </view>
            <view v-for="(item, index) in items" :key="item.id" class="flex mb-3">
              <!-- 选择框 -->
              <wd-checkbox
                :model-value="item.selected"
                @change="handleItemSelectChange(item.id, $event, item.hotel_name)"
                size="small"
                custom-class="flex! items-center"
              />
              <!-- 商品图片 -->
              <wd-img
                :src="item.imge.split(',')[0]"
                width="210rpx"
                height="192rpx"
                radius="8rpx"
                mode="aspectFill"
              >
                <template #error>
                  <view
                    class="w-210rpx h-192rpx bg-#f5f5f5 flex items-center justify-center rounded-lg"
                  >
                    <text class="text-#ccc text-xs">暂无图片</text>
                  </view>
                </template>
              </wd-img>
              <!-- 商品信息 -->
              <view class="flex-1 flex flex-col justify-between pl-3">
                <view class="flex flex-col gap-2 text-32rpx">
                  <view class="text-#333 text-ellipsis-2 mb-2">{{ item.name }}</view>
                  <view class="text-red-500">￥{{ item.price }}</view>
                </view>

                <!-- 数量调节器 -->
                <wd-input-number
                  :model-value="item.count"
                  @change="handleCountChange(item.id, $event)"
                  :max="item.goods_sum"
                  :min="0"
                  :step="1"
                  size="small"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部结算栏 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 pb-safe">
      <view class="flex items-center justify-between">
        <!-- 左侧：总价 -->
        <view class="flex flex-col px-4">
          <view class="flex items-end">
            <text class="text-red-500 text-lg font-bold mr-1">¥{{ selectedTotalPrice }}</text>
          </view>
          <text class="text-xs text-gray-400 line-through">¥{{ originalTotalPrice }}</text>
        </view>
        <!-- 右侧：确定购买按钮 -->
        <wd-button
          type="primary"
          size="large"
          :disabled="selectedCount === 0 || !canCheckout"
          @click="handleCheckout"
          custom-style="width: 60%;height-100%;--wot-button-large-radius:0px;"
          :round="false"
        >
          确定购买
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useShopCar } from '@/utils/hook/useShopCar'
import { getGoodsDetailByIds } from '@/pagesShop/api/index.js'
import { onLoad } from '@dcloudio/uni-app'

// 使用购物车hook
const { shopCarList, updateCarCount, toggleCarSelected, selectedCount, getCarItemIds } =
  useShopCar()

const cartItems = ref([])
// 页面加载
onLoad(() => {
  loadCartItems()
})

// 加载购物车商品详情
const loadCartItems = async () => {
  try {
    const itemIds = getCarItemIds()
    if (itemIds.length === 0) {
      cartItems.value = []
      return
    }
    const res = await getGoodsDetailByIds({ ids: itemIds.join(',') })
    const data = res?.data || []
    if (data.length > 0) {
      // 合并购物车数据和商品详情
      cartItems.value = shopCarList.value
        .map((carItem) => {
          const detail = data.find((item) => item.id === carItem.id)
          return {
            ...detail,
            ...carItem,
          }
        })
        .filter((item) => item.id)

      console.log(cartItems.value, 'cartItems')
    } else {
      cartItems.value = []
    }
  } catch (error) {
    cartItems.value = []
  }
}
// 按酒店分组商品
const groupedItems = computed(() => {
  const groups = {}
  cartItems.value.forEach((item) => {
    if (!groups[item.hotel_name]) {
      groups[item.hotel_name] = []
    }
    groups[item.hotel_name].push(item)
  })
  return groups
})

// 某个酒店是否全选
const isHotelAllSelected = (hotel_name) => {
  const hotelItems = cartItems.value.filter((item) => item.hotel_name === hotel_name)
  return hotelItems.length > 0 && hotelItems.every((item) => item.selected)
}

// 选中商品总价
const selectedTotalPrice = computed(() => {
  return cartItems.value
    .filter((item) => item.selected)
    .reduce((acc, item) => acc + parseFloat(item.price || 0) * item.count, 0)
    .toFixed(2)
})

// 原价总计
const originalTotalPrice = computed(() => {
  return cartItems.value
    .filter((item) => item.selected)
    .reduce((acc, item) => acc + parseFloat(item.original_price) * item.count, 0)
    .toFixed(2)
})

// 检查是否可以结算（单酒店约束）
const canCheckout = computed(() => {
  const selectedItems = cartItems.value.filter((item) => item.selected)
  if (selectedItems.length === 0) return false

  // 检查选中商品是否来自同一酒店
  const hotels = [...new Set(selectedItems.map((item) => item.hotel_name))]
  return hotels.length === 1
})

// 获取当前选中的酒店
const getSelectedHotel = () => {
  const selectedItems = cartItems.value.filter((item) => item.selected)
  if (selectedItems.length > 0) {
    return selectedItems[0].hotel_name
  }
  return ''
}

// 处理商品选择变化
const handleItemSelectChange = (id, selected, hotel_name) => {
  if (selected) {
    // 选中商品时，检查单酒店约束
    const currentSelectedHotel = getSelectedHotel()
    if (currentSelectedHotel && currentSelectedHotel !== hotel_name) {
      uni.showToast({
        title: `只能选择${currentSelectedHotel}的商品`,
        icon: 'none',
      })
      return
    }
  }

  // 更新hook状态
  toggleCarSelected(id, selected.value)
  // 同步更新本地数据
  const item = cartItems.value.find((item) => item.id === id)
  if (item) {
    item.selected = selected.value
  }
}

// 处理酒店分组选择变化
const handleHotelSelectChange = (hotel_name, selected) => {
  console.log(hotel_name, selected, 'hotel_name, selected')
  if (selected) {
    // 选中酒店时，先取消其他酒店的选择
    const currentSelectedHotel = getSelectedHotel()
    if (currentSelectedHotel && currentSelectedHotel !== hotel_name) {
      // 取消其他酒店的选择
      cartItems.value.forEach((item) => {
        if (item.hotel_name !== hotel_name && item.selected) {
          item.selected = false
          toggleCarSelected(item.id, false)
        }
      })
    }
  }

  // 更新当前酒店的选择状态
  cartItems.value.forEach((item) => {
    if (item.hotel_name === hotel_name) {
      item.selected = selected
      toggleCarSelected(item.id, selected)
    }
  })
}

// 处理数量变化
const handleCountChange = (id, newCount) => {
  if (newCount.value <= 0) {
    uni.showModal({
      title: '提示',
      content: '确定要删除该商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 删除商品
          const index = cartItems.value.findIndex((item) => item.id === id)
          if (index !== -1) {
            cartItems.value.splice(index, 1)
          }
          updateCarCount(id, 0)
        }
      },
    })
  } else {
    updateCarCount(id, newCount.value)
    // 同步更新本地数据
    const item = cartItems.value.find((item) => item.id === id)
    if (item) {
      item.count = newCount.value
    }
  }
}

// 去购物
const goShopping = () => {
  uni.navigateTo({
    url: `/pagesShop/pages/shop-type/index`,
  })
}

// 结算
const handleCheckout = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请选择要购买的商品',
      icon: 'none',
    })
    return
  }

  if (!canCheckout.value) {
    uni.showToast({
      title: '只能选择同一酒店的商品进行结算',
      icon: 'none',
    })
    return
  }
  let hotel_id = ''
  const selectedGood = cartItems.value
    .filter((item) => item.selected)
    .map((item) => {
      hotel_id = item.hotel_id
      return {
        id: item.id,
        count: item.count,
      }
    })
  uni.navigateTo({
    url: `/pagesShop/pages/good-order-detail/index?hotel_id=${hotel_id}&cart_items=${JSON.stringify(
      selectedGood,
    )}`,
  })
}
</script>

<style lang="scss">
.cart-page {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
