<template>
  <view class="goods-detail bg-#fafafa min-h-screen pb-20">
    <!-- 商品轮播图 -->
    <view class="relative">
      <wd-swiper v-if="goodsDetail.imge" :list="goodsimges" autoplay height="320" />
      <view v-else class="w-full h-320rpx bg-#f5f5f5 flex items-center justify-center">
        <text class="text-#ccc">暂无图片</text>
      </view>
    </view>

    <!-- 商品基本信息 -->
    <view class="bg-white mx-4 -mt-10 z-10 rounded-lg p-4 relative">
      <view class="flex items-start justify-between mb-4">
        <view class="flex-1 mr-4">
          <view class="text-#333 text-ellipsis-2 text-36rpx">
            {{ goodsDetail.name }}
          </view>
          <view class="mt-2">
            <view class="text-red-500 text-48rpx">￥{{ goodsDetail.price }}</view>
          </view>
        </view>
        <wd-button
          type="primary"
          plain
          size="small"
          @click="handleAddToCart"
          custom-style="border-color: #244190; color: #244190;"
        >
          添加购物车
        </wd-button>
      </view>
    </view>

    <!-- 店铺信息 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <view class="flex items-center justify-between">
        <view class="flex items-center flex-1">
          <wd-img
            src="/pagesShop/static/mall.png"
            width="48rpx"
            height="48rpx"
            custom-class="mr-3"
          />
          <text class="text-32rpx text-#333">{{ goodsDetail.hotel_name || '美亚酒店店铺' }}</text>
        </view>
        <view class="flex items-center" @click="handleGoToStore">
          <text class="text-28rpx text-#244190 mr-2">进店</text>
          <wd-icon name="arrow-right" size="16px" :color="'#244190'" />
        </view>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="bg-white mx-4 mt-4 rounded-lg p-4">
      <view class="text-32rpx font-medium text-#333 mb-4">商品详情</view>
      <view class="text-28rpx text-#666 leading-relaxed">
        <rich-text :nodes="goodsDetail.content" />
      </view>
    </view>

    <!-- 底部操作栏 -->
    <!-- <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 pb-safe px-4 pt-2">
      <view class="flex items-center justify-between">
        <view class="flex flex-col">
          <text class="text-24rpx text-#999">总计</text>
          <view class="text-red-500 text-36rpx">￥{{ goodsDetail.price }}</view>
        </view>

        <wd-button
          type="primary"
          size="large"
          @click="handleBuyNow"
          custom-style="background-color: #4979dc; border-color: #4979dc;"
        >
          确定购买
        </wd-button>
      </view>
    </view> -->

    <!-- 悬浮购物车 -->
    <ShopCar />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getGoodsDetail } from '@/pagesShop/api/index.js'
import { useShopCar } from '@/utils/hook/useShopCar'
import ShopCar from '@/components/shop-car.vue'

const { addCar } = useShopCar()

// 商品详情数据
const goodsDetail = ref({})
// 加载状态
const loading = ref(false)
// 商品ID
const goodsId = ref('')

// 商品图片列表
const goodsimges = computed(() => {
  if (!goodsDetail.value.imge) return []
  return goodsDetail.value.imge.split(',').filter((img) => img.trim())
})

// 页面加载
onLoad((options) => {
  if (options.id) {
    goodsId.value = options.id
    loadGoodsDetail()
  } else {
    uni.showToast({
      title: '商品ID不能为空',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

// 加载商品详情
const loadGoodsDetail = async () => {
  try {
    loading.value = true
    const res = await getGoodsDetail({ id: goodsId.value })

    if (res && res.data) {
      goodsDetail.value = {
        ...res.data,
      }
    } else {
      throw new Error('获取商品详情失败')
    }
  } catch (error) {
    console.error('加载商品详情失败:', error)
    uni.showToast({
      title: '加载商品详情失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 添加到购物车
const handleAddToCart = () => {
  if (!goodsDetail.value.id) {
    uni.showToast({
      title: '商品信息异常',
      icon: 'none',
    })
    return
  }

  addCar({ id: goodsDetail.value.id, count: 1 })
  uni.showToast({
    title: '已添加到购物车',
    icon: 'success',
  })
}

// 立即购买
const handleBuyNow = () => {
  if (!goodsDetail.value.id) {
    uni.showToast({
      title: '商品信息异常',
      icon: 'none',
    })
    return
  }

  // 先添加到购物车
  addCar({ id: goodsDetail.value.id, count: 1 })

  // 跳转到购物车页面
  uni.navigateTo({
    url: '/pagesShop/pages/shop-cart/index',
  })
}

// 进入店铺
const handleGoToStore = () => {
  uni.navigateTo({
    url: `/pagesShop/pages/shop-detail/index?id=${goodsDetail.value.hotel_id}&name=${goodsDetail.value.hotel_name}`,
  })
}
</script>

<style scoped lang="scss"></style>
