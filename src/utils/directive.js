// 因只定义Object注入的话，在全局注入时只触发一次，在这次触发中uni.getStorageSync获取为null，则之后使用一直为null，所以需定义为Function每次调用上传组件时重新获取配置
export default function (app) {
	
	 const headers = {
		source: 'applet',
		appid: initAppid()
	};

	// 图片上传配置注入
	app.provide('lgImageUpload', () => ({
		action: import.meta.env.VITE_API_URL + '/applet/common/upload/file',
		headers: { token: uni.getStorageSync('tm_token'), ...headers },
	}));
}
	const initAppid = () => {
		try {
			// #ifdef MP-WEIXIN
			return uni.getAccountInfoSync().miniProgram.appId;
			// #endif
			// #ifdef H5
			return 'wx6e6793ed835bb3a7';
			// #endif
		} catch (e) {
			console.log(e, '获取appid失败');
			return '';
		}
	};