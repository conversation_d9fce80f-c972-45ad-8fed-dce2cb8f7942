import Request from './luch-request/index.js'
import { useUser } from '../hook/useUser.js'
let isModal = false
const http = new Request()
export const headers = {
  source: 'applet',
}

const initAppid = () => {
	try {
		// #ifdef MP-WEIXIN
		return uni.getAccountInfoSync().miniProgram.appId;
		// #endif
		// #ifdef H5
		return 'wx6e6793ed835bb3a7';
		// #endif
	} catch (e) {
		console.log(e, '获取appid失败');
		return '';
	}
};
export const baseUrl = import.meta.env.VITE_API_URL; /* 根域名不同 */
http.setConfig((config) => {
  /* 设置全局配置 */
  config.baseURL = baseUrl
  config.header = {
    ...config.header,
    ...headers,
    appid: initAppid(),
  }
  return config
})
http.interceptors.request.use(
  (config) => {
    /* 请求之前拦截器。可以使用async await 做异步操作 */
    if (uni.getStorageSync('tm_token')) {
      config.header['token'] = `${uni.getStorageSync('tm_token')}`
    }
    return config
  },
  (config) => {
    return Promise.reject(config)
  },
)

http.interceptors.response.use(
  async (response) => {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    let route = ''
    if (currentPage) {
      route = currentPage.route
    }

    /* 请求之后拦截器。可以使用async await 做异步操作  */
    const res = response.data
    if (res.code && res.code !== 200) {
      // `token` 过期或者账号已在别处登录
      if (res.code === 401 || res.code === 403 || res.code === 400) {
        uni.clearStorageSync()
        const { setUserStatus } = useUser()
        setUserStatus()
        if (!isModal && (!route || route != 'pages/my/index')) {
          isModal = true
          uni.showModal({
            title: '提示',
            content: res.msg ? res.msg : '你已被登出，请重新登录',
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
                isModal = false
                uni.navigateTo({
                  url: '/pages/my/index',
                })
              }
            },
          })
        }
      }
      return Promise.reject(res)
    } else {
      return res
    }
  },
  (response) => {
    // 请求错误做点什么。可以使用async await 做异步操作
    console.log(response)
    if (response.data && response.statusCode && response.statusCode == 429) {
      return Promise.reject({ code: '500', msg: response.data })
    } else {
      return Promise.reject(false)
    }
  },
)
// 请求方法
export async function request(url, deploy = {}, wrong = true, time = 100) {
  return new Promise((resolve, reject) => {
    try {
      http
        .middleware({
          method: 'POST', // 请求方法必须大写 [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE|UPLOAD|DOWNLOAD]
          url: url,
          ...deploy,
        })
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          if (wrong) {
            setTimeout(() => {
              uni.showToast({
                icon: 'none',
                title: err.msg ? JSON.stringify(err.msg).replace(/\"/g, '') : '请求失败',
                duration: 2000,
              })
            }, time)
          }
          reject(err)
        })
    } catch (err) {
      setTimeout(() => {
        uni.showToast({
          icon: 'none',
          title: '请求错误',
          duration: 2000,
        })
      }, time)
      reject(false)
    }
  })
}
// 上传方法
export async function upload(url, payload) {
  return new Promise((resolve, reject) => {
    try {
      uni.uploadFile({
        url: `${baseUrl}` + url,
        filePath: payload || [],
        header: {
          ...headers,
          appid: initAppid(),
          token: `${uni.getStorageSync('tm_token')}`,
        },
        name: 'file',
        success: (response) => {
          const res = JSON.parse(response.data)
          if (res.code == 200) {
            resolve(res)
          } else {
            reject(false)
          }
        },
        fail: (err) => {
          console.log(err)
          reject(false)
        },
      })
    } catch (e) {
      reject(false)
    }
  })
}
