import { useUser } from '/src/utils/hook/useUser.js';
import { setActivity, setShare } from '/src/api/unify.js';
import { throttle } from '/src/utils/utils.js';

export const onShare = (e) => {
	if (e) {
		setShare({ task_id: e, mark_id: generateOrderNumber() });
	}
};

export function generateOrderNumber() {
	const now = new Date();
	const year = now.getFullYear().toString().padStart(4, '0');
	const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，需要加1
	const day = now.getDate().toString().padStart(2, '0');
	const hours = now.getHours().toString().padStart(2, '0');
	const minutes = now.getMinutes().toString().padStart(2, '0');
	const seconds = now.getSeconds().toString().padStart(2, '0');
	const milliseconds = now.getMilliseconds().toString().padStart(3, '0');
	const formattedDate = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
	const randomSuffix = Math.floor(Math.random() * 1000)
		.toString()
		.padStart(3, '0');
	const orderNumber = formattedDate + randomSuffix.slice(-3);
	return orderNumber;
}

export const onChange = (e, callBack = () => {}) => {
	const { deploy } = e;
	if (!deploy) return false;
	if (deploy.type == 'page' || deploy.type == 'customizePage') return openPage(deploy);
	if (deploy.type == 'link') return openLink(deploy);
	if (deploy.type == 'telephone') return openPhone(deploy);
	if (deploy.type == 'applet') return openApplet(deploy);
	if (deploy.type == 'article') return openArticle(e);
	if (deploy.type == 'coupon') return openCoupon(e, callBack);

	console.log(deploy);
	// pages/article/index
};

const openCoupon = (e, callBack) => {
	const { isLogin, setLogin } = useUser();
	uni.showLoading({ mask: true });
	setTimeout(function () {
		uni.hideLoading();
	}, 2000);
	throttle(() => {
		if (isLogin.value) {
			setActivity({ article_id: e.article_id, activity_id: e.id, type: e.activity_type, coupon_id: e.deploy.query.id })
				.then((res) => {
					setTimeout(() => {
						uni.showToast({
							icon: 'none',
							title: '领取成功',
							duration: 2000
						});
					}, 200);
					callBack(res);
				})
				.finally(() => {
					uni.hideLoading();
				});
		} else {
			setLogin();
		}
	});
	//
};

// 打开文章
const openArticle = (e) => {
	if (e.deploy.url) {
		uni.navigateTo({
			url: `${e.deploy.url}${objectToQueryString({ ...e.deploy.query, activity_id: e.id, type: e.activity_type })}`
		});
	}
};

// 打开小程序
const openApplet = (e) => {
	if (e.url) {
		let extraData = {};
		try {
			extraData = JSON.parse(e.query);
		} catch (error) {
			console.log(error, '字段错误');
		}

		uni.navigateToMiniProgram({
			appId: e.url,
			path: e.path,
			extraData: extraData,
			success(res) {
				// 打开成功
			}
		});
	}
};
// 打电话
const openPhone = (e) => {
	if (e.url) {
		uni.makePhoneCall({
			phoneNumber: e.url
		});
	}
};

// 跳转链接
const openLink = (e) => {
	if (e.url) {
		uni.reLaunch({
			url: `/pages/link/index?url=${e.url}`
		});
	}
};

// 跳转页面
const openPage = (e) => {
	if (e.url) {
		let list = ['/pages/index/index', '/pages/coupon/index', '/pages/member/index', '/pages/my/index'];
		if (list.some((res) => res == e.url)) {
			uni.reLaunch({
				url: `${e.url}${objectToQueryString(e.query)}`
			});
		} else {
			uni.navigateTo({
				url: `${e.url}${objectToQueryString(e.query)}`
			});
		}
	}
};
function objectToQueryString(obj) {
	let parts = [];
	if (obj.constructor === Object) {
		for (const key in obj) {
			parts = [...parts, `${key}=${obj[key]}`];
		}
	}
	if (parts && parts.length) {
		return `?${parts.join('&')}`;
	} else {
		return '';
	}
}
