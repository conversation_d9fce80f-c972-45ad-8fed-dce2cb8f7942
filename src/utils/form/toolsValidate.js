export default {
    // 电话号码
    telephone: {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: '电话号码错误'
    },
    // 用户账号
    userName: {
        pattern: /^[0-9a-zA-Z_]{5,15}$/,
        message: '用户名错误'
    },
    // 密码
    password: {
        pattern: /^[0-9A-Za-z.@\-\_]{6,16}$/,
        message: '密码错误',
        trigger: ['change']
    },
    //强密码
    passwordPowerful: {
        pattern: /^(?![a-zA-z]+$)(?!\d+$)(?![!@#$%^&\.*]+$)(?![a-zA-z\d]+$)(?![a-zA-z!@#$%^&\.*]+$)(?![\d!@#$%^&\.*]+$)[a-zA-Z\d!@#$%^&\.*]{6,16}$/,
        message: '密码错误'
    },
    // 路径
    path: {
        pattern: /^[0-9a-zA-Z_/.]{1,100}$/,
        message: '路径错误'
    },

    // 卡号
    card: {
        pattern: /^[0-9A-Za-z.@\-\_]{2,16}$/,
        message: '卡号错误'
    },
    // ip地址
    ip: {
        pattern:
            /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
        message: 'ip地址错误'
    },

    //身份证
    idCard: {
        pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '身份证号错误'
    },
    //邮箱
    email: {
        pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
        message: '邮箱错误'
    },

    //姓名
    fullName: {
        pattern: /^[\u4e00-\u9fa5]{1,6}(·[\u4e00-\u9fa5]{1,6}){0,2}$/,
        message: '姓名错误'
    },
    //邮政编码
    postalCode: {
        pattern: /^[1-9][0-9]{5}$/,
        message: '邮政编码错误'
    },
    //车牌号
    carNum: {
        // pattern: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/,
        pattern:
            /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][0-9]{5}[DF])$/,
        message: '车牌号错误'
    },

    //汉字
    chinese: {
        pattern: /^[\u4e00-\u9fa5]{0,}$/,
        message: '输入错误'
    },
    // 数组
    array: {
        type: 'array',
        required: true,
        message: '请输入'
    },
    required: {
        required: true,
        message: '请输入'
    },
    // 对象
    object: {
        type: 'object',
        validator: (rule, value, callback) => {
            if (JSON.stringify(value) == '{}') {
                return Promise.reject('请选择');
            } else {
                return Promise.resolve();
            }
        },
        required: true
    },
    //数字
    number: {
        pattern: /^[0-9]*$/,
        message: '输入错误'
    },

    // 英文数字下划线
    ordinaryString: {
        pattern: /^[0-9a-zA-Z_]{1,30}$/,
        message: '输入错误'
    },

    //正浮点数
    floating: {
        pattern: /^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/,
        message: '输入错误'
    },
    //浮点数
    decimal: {
        pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/,
        message: '输入错误'
    },
    //英文和数字
    englishNumbers: {
        pattern: /^[A-Za-z0-9]+$/,
        message: '输入错误'
    }
};
