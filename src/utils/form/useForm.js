import Schema from './async-validator.js';
import toolsValidate from './toolsValidate.js';
import { ref } from 'vue';
export function useForm() {
    const form = ref(null);
    const rules = ref(null);
	/**
	* 检验表单
	* <AUTHOR> 张学勇
	* @date: 2024-02-19 15:08:57
	* @param form 表单对象
	* @param rules 表单规则
	* @return 检验结果
	*/
    const validate = (e = form.value, a = rules.value) => {
        return new Promise((resolve, reject) => {
            const validator = new Schema(formatrules(a));
            validator
                .validate(e)
                .then(() => {
                    resolve(true);
                })
                .catch(({ errors, fields }) => {
                    uni.showToast({
                        icon: 'none',
                        title: errors[0].message,
                        duration: 2000
                    });
                    reject(errors, fields);
                });
        });
    };
    /**
     * 分割规则
     * <AUTHOR> 张学勇
     * @date: 2024-02-19 15:07:55
     * @param 规则对象
     * @return 格式化后的对象
     */
    const formatrules = (e) => {
        const rules_list = {};
        for (const item in e) {
            rules_list[item] = StringTransferObj(e[item]);
        }
        return rules_list;
    };
    /**
     * 格式化规则
     * <AUTHOR> 张学勇
     * @date: 2024-02-19 15:07:55
     * @param 规则对象
     * @return 格式化后的对象
     */
    const StringTransferObj = (rules) => {
        const rules_list = [].concat(rules || []);
        let rules_item = [];
        if (rules_list.length) {
            for (const item of rules_list) {
                if (typeof item == 'string') {
                    if (toolsValidate[item]) {
                        const Validate_item = { ...toolsValidate[item] };
                        rules_item = [...rules_item, ...[Validate_item]];
                    }
                } else if (Array.isArray(item)) {
                    if (toolsValidate[item[0]]) {
                        const Validate_item = { ...toolsValidate[item[0]] };
                        if (item[1]) {
                            Validate_item.message = item[1];
                        }
                        rules_item = [...rules_item, ...[Validate_item]];
                    }
                } else if (item.constructor === Object) {
                    if (item.defaultField) {
                        rules_item = [...rules_item, ...[{ ...item, ...{ defaultField: StringTransferObj(item.defaultField) } }]];
                    } else if (item.fields) {
                        rules_item = [...rules_item, ...[{ ...item, ...{ fields: formatrules(item.fields) } }]];
                    } else {
                        rules_item = [...rules_item, ...[item]];
                    }
                }
            }
        }
        return rules_item;
    };
    return { form, rules, validate };
}
