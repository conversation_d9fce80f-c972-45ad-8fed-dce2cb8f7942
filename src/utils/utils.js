let flag = false
import QQMapWX from './qqmap-wx-jssdk.js'
import * as lg from '@/uni_modules/wot-design-uni'
const qqmapsdk = new QQMapWX({
  key: 'RXIBZ-OQGEN-ATSF4-S6KEU-GF7KZ-6PFKG',
})
export { lg }

/**
 *
 * 节流原理：在一定时间内，只能触发一次
 *
 * @param {Function} func 要执行的回调函数
 * @param {Number} wait 延时的时间
 * @param {Boolean} immediate 是否立即执行
 * @return null
 */
export function throttle(func, wait = 1500, immediate = true) {
  if (immediate) {
    if (!flag) {
      flag = true
      // 如果是立即执行，则在wait毫秒内开始时执行
      typeof func === 'function' && func()
      setTimeout(() => {
        flag = false
      }, wait)
    }
  } else {
    if (!flag) {
      flag = true
      // 如果是非立即执行，则在wait毫秒内的结束处执行
      setTimeout(() => {
        flag = false
        typeof func === 'function' && func()
      }, wait)
    }
  }
}

/**
 * 判断值为空返回 --
 * @param value 入参
 * @returns {string|*} 返回值
 */
export function checkEmpty(value) {
  if (value === 0) {
    return value
  }
  return !value ? '--' : value
}

/**
 * Toast提示
 * @param title
 * @param icon
 * @param duration
 */
export function showToast(title, icon = 'none', duration = 2000) {
  uni.showToast({ icon, title, duration })
}

/**
 * 区分支付基座环境
 */
export function checkPayEnvironment() {
  return new Promise((resolve, reject) => {
    uni.getProvider({
      service: 'payment',
      success: function (res) {
        // 判断支付基座环境
        if (~res.provider.indexOf('wxpay')) {
          // 微信
          resolve({ type: 'wxpay', typeCode: 1 })
        } else if (~res.provider.indexOf('alipay')) {
          // 支付宝
          resolve({ type: 'alipay', typeCode: 2 })
        } else {
          reject()
        }
      },
    })
  })
}
/**
 * 检验时间是否超过一小时
 * <AUTHOR> 张学勇
 * @date: 2023-11-22 16:16:23
 * @param
 * @return
 */
export function timeFrom(dateTime = null) {
  if (!dateTime) dateTime = Number(new Date())
  if (dateTime.toString().length == 10) dateTime *= 1000
  const timestamp = +new Date(Number(dateTime))
  const timer = (Number(new Date()) - timestamp) / 1000
  let tips = ''
  switch (true) {
    case timer < 3600:
      tips = false
      break
    case timer >= 3600:
      tips = true
      break
  }
  return tips
}

/**
 * 计算两个日期之间相差的天数
 * @param {string|number|Date} date1 第一个日期（支持字符串、时间戳、Date对象）
 * @param {string|number|Date} date2 第二个日期（支持字符串、时间戳、Date对象）
 * @returns {number} 返回相差的绝对天数
 * @example
 * getDaysDiff('2024-12-20', '2024-12-19') // 返回 1
 * getDaysDiff(new Date('2024-12-20'), new Date('2024-12-15')) // 返回 5
 * getDaysDiff('2024-12-19', '2024-12-25') // 返回 6
 */
export function getDaysDiff(date1, date2) {
  try {
    // 参数验证
    if (!date1 || !date2) {
      console.warn('getDaysDiff: 日期参数不能为空')
      return 0
    }

    // 转换为Date对象
    const d1 = new Date(date1)
    const d2 = new Date(date2)

    // 验证日期有效性
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
      console.warn('getDaysDiff: 无效的日期格式')
      return 0
    }

    // 将日期设置为当天的开始时间（00:00:00），避免时间部分影响天数计算
    d1.setHours(0, 0, 0, 0)
    d2.setHours(0, 0, 0, 0)

    // 计算毫秒差值
    const timeDiff = Math.abs(d1.getTime() - d2.getTime())

    // 转换为天数（1天 = 24 * 60 * 60 * 1000 毫秒）
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

    return daysDiff
  } catch (error) {
    console.error('getDaysDiff 计算错误:', error)
    return 0
  }
}

/**
 * 计算两个日期之间相差的天数（带方向）
 * @param {string|number|Date} startDate 开始日期
 * @param {string|number|Date} endDate 结束日期
 * @returns {number} 返回相差的天数，正数表示endDate在startDate之后，负数表示之前
 * @example
 * getDaysDiffWithDirection('2024-12-19', '2024-12-20') // 返回 1
 * getDaysDiffWithDirection('2024-12-20', '2024-12-19') // 返回 -1
 */
export function getDaysDiffWithDirection(startDate, endDate) {
  try {
    // 参数验证
    if (!startDate || !endDate) {
      console.warn('getDaysDiffWithDirection: 日期参数不能为空')
      return 0
    }

    // 转换为Date对象
    const d1 = new Date(startDate)
    const d2 = new Date(endDate)

    // 验证日期有效性
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
      console.warn('getDaysDiffWithDirection: 无效的日期格式')
      return 0
    }

    // 将日期设置为当天的开始时间
    d1.setHours(0, 0, 0, 0)
    d2.setHours(0, 0, 0, 0)

    // 计算毫秒差值（保持方向）
    const timeDiff = d2.getTime() - d1.getTime()

    // 转换为天数
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

    return daysDiff
  } catch (error) {
    console.error('getDaysDiffWithDirection 计算错误:', error)
    return 0
  }
}
