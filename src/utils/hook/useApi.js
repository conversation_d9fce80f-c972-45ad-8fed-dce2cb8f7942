import { provide, ref } from 'vue';
export function useApi(queueKey='lgapi') {
	const apiFun = ref(null);
	function pushApi(comp) {
		console.log(comp,'添加');
		apiFun.value = comp;
	}
	function removeFromApi(comp) {
		console.log(comp,'删除');
		apiFun.value = null;
	}
	function getApi() {
		if (apiFun.value) {
			return apiFun.value.$.exposed
		}
	}
	provide(queueKey, {
		apiFun,
		pushApi,
		removeFromApi,
		getApi
	});
	return {
		getApi
	};
}
