/**
 *用户信息公共-hooks
 * <AUTHOR> 张学勇
 * @date: 2023-11-24 15:26:40
 */
import { getUserInfo, getUserCode } from '/src/api/unify.js';
import { ref } from 'vue';

const userName = uni.getStorageSync('tm_user');

// 身份信息
const role = ref(userName);
// 用户信息
const user = ref(userName);
// 是否登录
const isLogin = ref(uni.getStorageSync('tm_token') ? true : false);
const loginFun = ref(() => {});
export function useUser() {
	/**
	 * 设置用户信息
	 * <AUTHOR> 张学勇
	 * @date: 2023-11-24 15:47:24
	 */
	const setUser = () => {
		user.value = uni.getStorageSync('tm_user');
	};
	/**
	 * 设置登录状态
	 * <AUTHOR> 张学勇
	 * @date: 2023-11-24 15:47:07
	 */
	const setIsLogin = () => {
		isLogin.value = uni.getStorageSync('tm_token') ? true : false;
	};
	/**
	 * 初始化获取用户信息
	 * @author: 张学勇
	 * @date: 2024-04-07 23:35:42
	 */
	const initUserInfo = (isRefresh = false,callBack = () => {}) => {
		if (uni.getStorageSync('tm_token')) {
			getUserInfo().then((res) => {
				uni.setStorageSync('tm_user', res.data.user);
				setUserStatus();
				callBack();
				if (isRefresh) {
					uni.stopPullDownRefresh();
				}
			});
		} else {
			wx.login({
				success: function (res) {
					getUserCode({ code: res.code }).then((res) => {
						uni.setStorageSync('tm_user', res.data);
						setUserStatus();
					});
				}
			});
		}
	};

	const initCode = () => {
		return new Promise((resolve, reject) => {
			try {
				if (user.value && user.value.openid) {
					resolve(user.value);

					return;
				}
				wx.login({
					success: function (res) {
						getUserCode({ code: res.code }).then((res) => {
							uni.setStorageSync('tm_user', res.data);
							setUserStatus();
							resolve(res.data);
						});
					}
				});
			} catch (e) {
				uni.hideLoading();
				setTimeout(() => {
					uni.showToast({
						icon: 'none',
						title: '获取用户消息失败',
						duration: 2000
					});
				}, 200);
				reject(false);
			}
		});
	};
	const setModelLogin = () => {
		 loginFun.value();
	};

	const setLogin = () => {
		 loginFun.value();
		// uni.reLaunch({
		// 	url: '/pages/login/index'
		// });
		// loginFun.value();
	};
	/**
	 * 更新用户信息,登录状态
	 * <AUTHOR> 张学勇
	 * @date: 2023-11-24 15:49:13
	 */
	const setUserStatus = () => {
		setUser();
		setIsLogin();
	};

	/**
	 * 登出，清空缓存并跳转首页
	 */
	const logout = () => {
		uni.clearStorageSync();
		setUserStatus();
		uni.reLaunch({
			url: '/pages/index/index'
		});
	};

	return {
		initUserInfo,
		user,
		role,
		isLogin,
		setUser,
		setIsLogin,
		setUserStatus,
		logout,
		setLogin,
		loginFun,
		initCode,
		setModelLogin
	};
}
