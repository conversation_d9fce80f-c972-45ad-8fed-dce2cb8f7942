import { ref, computed } from 'vue'

const shopCarList = ref([
  {
    id: '704036563542149',
    count: 2,
    selected: true,
  },
  {
    id: '704035593961605',
    count: 3,
    selected: true,
  },
])

export function useShopCar() {
  // 添加商品到购物车
  function addCar(item) {
    const index = shopCarList.value.findIndex((existingItem) => existingItem.id === item.id)
    if (index !== -1) {
      shopCarList.value[index].count += item.count || 1
    } else {
      shopCarList.value.push({
        id: item.id,
        count: item.count || 1,
        selected: true, // 默认选中
      })
    }
    console.log('购物车更新:', shopCarList.value)
  }

  // 删除商品
  function removeCar(id) {
    const index = shopCarList.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      shopCarList.value.splice(index, 1)
    }
  }

  // 更新商品数量
  function updateCarCount(id, count) {
    const index = shopCarList.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      if (count <= 0) {
        removeCar(id)
      } else {
        shopCarList.value[index].count = count
      }
    }
  }

  // 切换商品选中状态
  function toggleCarSelected(id, selected) {
    const index = shopCarList.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      shopCarList.value[index].selected =
        selected !== undefined ? selected : !shopCarList.value[index].selected
    }
  }

  // 全选/反选
  function toggleAllSelected(selected) {
    shopCarList.value.forEach((item) => {
      item.selected = selected
    })
  }

  // 清空购物车
  function clearCar() {
    shopCarList.value = []
  }

  // 获取购物车商品ID列表
  function getCarItemIds() {
    return shopCarList.value.map((item) => item.id)
  }

  // 总商品数量
  const totalCount = computed(() => {
    return shopCarList.value.reduce((acc, item) => acc + item.count, 0)
  })

  // 选中商品数量
  const selectedCount = computed(() => {
    return shopCarList.value
      .filter((item) => item.selected)
      .reduce((acc, item) => acc + item.count, 0)
  })

  // 是否全选
  const isAllSelected = computed(() => {
    return shopCarList.value.length > 0 && shopCarList.value.every((item) => item.selected)
  })

  return {
    shopCarList,
    addCar,
    removeCar,
    updateCarCount,
    toggleCarSelected,
    toggleAllSelected,
    clearCar,
    getCarItemIds,
    totalCount,
    selectedCount,
    isAllSelected,
  }
}
