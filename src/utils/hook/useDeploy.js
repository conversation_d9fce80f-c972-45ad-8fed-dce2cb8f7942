/**
 *用户配置公共-hooks
 * <AUTHOR> 张学勇
 * @date: 2023-11-24 15:26:40
 */
import { getPlatformDeployList } from '/src/api/unify.js';
import { ref } from 'vue';

const platformDeployList = ref([]);
const platformDeployObj = ref([]);
export function useDeploy() {
    /**
     * 初始化获取用户信息
     * @author: 张学勇
     * @date: 2024-04-07 23:35:42
     */
    const initPlatformDeployList = () => {
        getPlatformDeployList().then((res) => {
            platformDeployList.value = res.data;
			
			if(res.data&&res.data.length){
				for(let item of res.data){
					platformDeployObj.value[item.name]=item
				}
			}
        });
    };

    return {
		initPlatformDeployList,
        platformDeployList,
		platformDeployObj
    };
}
